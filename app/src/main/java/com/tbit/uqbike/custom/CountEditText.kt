package com.tbit.uqbike.custom

import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.tbit.uqbike.R


/**
 * Created by yankaibang on 2018/5/29.
 */
class CountEditText : ConstraintLayout {

    val edit: EditText
    val tvCount: TextView
    var maxlength: Int = Int.MAX_VALUE

    @JvmOverloads
    constructor(context: Context, attrset: AttributeSet? = null, defStyleAttr: Int = 0) : super(context, attrset, defStyleAttr) {
        val attrs = intArrayOf(android.R.attr.textSize, android.R.attr.textColor
                , android.R.attr.background, android.R.attr.text
                , android.R.attr.hint, android.R.attr.maxLength)
        val a = context.theme.obtainStyledAttributes(attrset, attrs, defStyleAttr, 0)
        val backgroundDrawable = a.getDrawable(attrs.indexOf(android.R.attr.background))
        val maxLength = a.getInt(attrs.indexOf(android.R.attr.maxLength), Int.MAX_VALUE)
        val textSize = a.getDimensionPixelSize(attrs.indexOf(android.R.attr.textSize), dp2px(context, 16f))
        val textColor = a.getColor(attrs.indexOf(android.R.attr.textColor), Color.BLACK)
        val text = a.getText(attrs.indexOf(android.R.attr.text)) ?: ""
        val hint = a.getText(attrs.indexOf(android.R.attr.hint)) ?: ""
        a.recycle()

        LayoutInflater.from(context).inflate(R.layout.layout_count_edit, this, true)
        edit = findViewById<EditText>(R.id.edit)
        tvCount = findViewById<TextView>(R.id.tv_count)

        background = backgroundDrawable
        setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize.toFloat())
        setTextColor(textColor)
        setMaxLength(maxLength)
        setText(text)
        setHint(hint)

        initListener()
    }

    fun setText(text: CharSequence) {
        edit.setText(text)
    }

    fun setText(resid: Int) {
        edit.setText(resid)
    }

    fun getText(): Editable? {
        return edit.text
    }

    fun setTextSize(unit: Int, size: Float) {
        edit.setTextSize(unit, size)
    }

    fun setTextColor(color: Int) {
        edit.setTextColor(color)
    }

    fun setHint(hint: CharSequence) {
        edit.hint = hint
    }

    fun setMaxLength(maxLength: Int) {
        if (maxLength >= 0) {
            maxlength = maxLength
            edit.setFilters(arrayOf<InputFilter>(InputFilter.LengthFilter(maxLength)))
        } else {
            maxlength = Int.MAX_VALUE
            edit.setFilters(arrayOfNulls(0))
        }
        updateCount()
    }

    private fun initListener() {
        edit.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                updateCount()
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }
        })
    }

    private fun updateCount() {
        if (maxlength == Int.MAX_VALUE) {
            tvCount.text = ""
            tvCount.visibility = View.GONE
        } else {
            tvCount.text = "${edit.length()}/$maxlength"
            tvCount.visibility = View.VISIBLE
        }
    }

    /**
     * dp 转 px
     *
     * @param dpValue dp 值
     * @return px 值
     */
    private fun dp2px(context: Context, dpValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }
}