package com.tbit.uqbike.custom

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import com.tbit.uqbike.R

class CostFlowPopView (context: Context, w: Int, h: Int) : PopupWindow() {
    init {
        setBackgroundDrawable(ContextCompat.getDrawable(context, android.R.color.transparent))
        width = w
        height = h
        contentView = LayoutInflater.from(context).inflate(R.layout.popupwindow_country_code, null)
        isOutsideTouchable = true
        isFocusable = true
        isClippingEnabled = true
    }

    override fun showAsDropDown(anchor: View?, xoff: Int, yoff: Int, gravity: Int) {
        super.showAsDropDown(anchor, xoff, yoff, gravity)
    }

    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        super.showAtLocation(parent, gravity, x, y)
    }
}