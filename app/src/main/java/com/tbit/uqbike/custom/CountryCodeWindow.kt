package com.tbit.uqbike.custom

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.CountryCodeListAdapter
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.mvp.model.CountryCodeModel
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import org.json.JSONObject

class CountryCodeWindow(context: Context, w: Int, h: Int) : PopupWindow() {
    private var adapter = CountryCodeListAdapter()
    init {
        setBackgroundDrawable(ContextCompat.getDrawable(context, android.R.color.transparent))
        width = w
        height = h
        contentView = LayoutInflater.from(context).inflate(R.layout.popupwindow_country_code, null)
        isOutsideTouchable = true
        isFocusable = true
        isClippingEnabled = true

        contentView.findViewById<RecyclerView>(R.id.rcv_country_code).layoutManager = LinearLayoutManager(context)
        contentView.findViewById<RecyclerView>(R.id.rcv_country_code).adapter = adapter
//        contentView.rcv_country_code.addItemDecoration(DividerItemDecoration(context, LinearLayoutManager.VERTICAL))

        contentView.findViewById<LinearLayout>(R.id.bg_login_country).setOnClickListener { dismiss() }
        this.setOnDismissListener {
//            MyLogUtil.Log("0000","========showAtLocation 0========="+adapter.selQH)
            if (!eventName.isNullOrEmpty()){
                val properties = JSONObject()
                properties.put("tag",eventName)
                properties.put("area_code_id",adapter.selQH)
                MDUtil.pageEnd(eventName,properties)
            }
        }
    }

    override fun showAsDropDown(anchor: View?, xoff: Int, yoff: Int, gravity: Int) {
        super.showAsDropDown(anchor, xoff, yoff, gravity)
        notifyDataSetChange()
    }
    var eventName = "select_area_code"
    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        super.showAtLocation(parent, gravity, x, y)
        notifyDataSetChange()
        adapter.selQH = ""
//        MyLogUtil.Log("0000","========showAtLocation 1=========")
        if (!eventName.isNullOrEmpty()) MDUtil.pageStar(eventName)
    }


    private fun notifyDataSetChange() {
        adapter.source = CountryCodeModel.getAllCountryCodeInfo()
        adapter.notifyDataSetChanged()
    }

    fun setOnItemClickListener(listener: (CountryDataItem) -> Unit) {
        adapter.onItemClickListener = listener
    }
}