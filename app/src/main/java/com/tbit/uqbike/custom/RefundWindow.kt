package com.tbit.uqbike.custom

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.RefundKhhAdapter

class RefundWindow(context: Context, w: Int, h: Int) : PopupWindow() {
    private var adapter = RefundKhhAdapter()

    init {
        setBackgroundDrawable(ContextCompat.getDrawable(context, android.R.color.transparent))
        width = w
        height = h
        contentView = LayoutInflater.from(context).inflate(R.layout.popupwindow_refund, null)
        isOutsideTouchable = true
        isFocusable = true
        isClippingEnabled = true

        contentView.findViewById<RecyclerView>(R.id.rcv_country_code).layoutManager = LinearLayoutManager(context)
        contentView.findViewById<RecyclerView>(R.id.rcv_country_code).adapter = adapter
//        contentView.rcv_country_code.addItemDecoration(DividerItemDecoration(context, LinearLayoutManager.VERTICAL))

        contentView.findViewById<LinearLayout>(R.id.bg_login_country).setOnClickListener { dismiss() }
    }

    override fun showAsDropDown(anchor: View?, xoff: Int, yoff: Int, gravity: Int) {
        super.showAsDropDown(anchor, xoff, yoff, gravity)
        notifyDataSetChange()
    }

    override fun showAtLocation(parent: View?, gravity: Int, x: Int, y: Int) {
        super.showAtLocation(parent, gravity, x, y)
        notifyDataSetChange()
    }

    private fun notifyDataSetChange() {
        adapter.source = source
        adapter.notifyDataSetChanged()
    }

    private var source: ArrayList<String> = ArrayList<String>()
    fun setData(source: ArrayList<String>){
        this.source = source
    }

    fun setOnItemClickListener(listener: (String) -> Unit) {
        adapter.onItemClickListener = listener
    }
}