package com.tbit.uqbike.custom;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by WG on 27/01/2018.
 * Email: <EMAIL>
 * Github: https://github.com/WGwangguan
 * Desc:
 */

public class InterceptFrameLayout extends FrameLayout {

    private boolean intercept;

    public InterceptFrameLayout(@NonNull Context context) {
        this(context, null);
    }

    public InterceptFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public InterceptFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setIntercept(boolean intercept) {
        this.intercept = intercept;
    }

    public boolean isIntercept() {
        return intercept;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return intercept;
    }

    public static InterceptFrameLayout bindView(View view) {
        InterceptFrameLayout interceptFrameLayout = new InterceptFrameLayout(view.getContext());
        ViewGroup parent = (ViewGroup) view.getParent();
        if (parent != null) {
            int childIndex = parent.indexOfChild(view);
            parent.removeViewAt(childIndex);
            interceptFrameLayout.addView(view);
            parent.addView(interceptFrameLayout, childIndex);
        }
        return interceptFrameLayout;
    }

}
