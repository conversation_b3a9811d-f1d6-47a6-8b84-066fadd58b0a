package com.tbit.installhelper.ble

import android.os.Handler
import android.os.Message
import android.os.SystemClock
import java.util.*

class AutoClearMap<K, V>(private val wrapper: MutableMap<K, V>, private val holdTime: Long = 5000): MutableMap<K, V> by wrapper {

    private val clearMap = TreeMap<K, Long>()
    private val MESSAGE_CLEAN = 0
    var onAutoClear = { _: K ->}

    private val handler = object : Handler() {
        override fun handleMessage(msg: Message) {
            autoClearMap()
        }
    }

    override fun clear() {
        wrapper.clear()
        clearMap.clear()
        handler.removeMessages(MESSAGE_CLEAN)
    }

    override fun put(key: K, value: V): V? {
        val put = wrapper.put(key, value)
        clearMap[key] = SystemClock.elapsedRealtime() + holdTime
        sendClearMessage()
        return put
    }

    override fun putAll(from: Map<out K, V>) {
        wrapper.putAll(from)
        from.keys.forEach { k ->  clearMap[k] = SystemClock.elapsedRealtime() + holdTime}
        sendClearMessage()
    }

    private fun sendClearMessage() {
        if(clearMap.isEmpty()) return
        val delayTime = clearMap.firstEntry().value - SystemClock.elapsedRealtime()
        handler.removeMessages(MESSAGE_CLEAN)
        handler.sendEmptyMessageDelayed(MESSAGE_CLEAN, delayTime)
    }

    private fun autoClearMap() {
        val elapsedRealtime = SystemClock.elapsedRealtime()
        val iterator = clearMap.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if(entry.value <= elapsedRealtime) {
                remove(entry.key)
                iterator.remove()
                onAutoClear(entry.key)
            } else {
                break
            }
        }
        sendClearMessage()
    }
}