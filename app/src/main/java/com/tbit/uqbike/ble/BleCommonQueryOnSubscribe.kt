package com.tbit.installhelper.ble

import android.util.Log
import com.tbit.tbitblesdk.Bike.ResultCode
import com.tbit.tbitblesdk.Bike.TbitBle
import com.tbit.tbitblesdk.protocol.Packet
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe

/**
 * Created by yankaibang on 2019/4/26.
 */
abstract class BleCommonQueryOnSubscribe<T> : ObservableOnSubscribe<T> {

    abstract protected val commandId: Byte
    abstract protected val key: Byte
    abstract protected val value: Array<Byte>?
    abstract protected val errorMsg: String?
    abstract protected fun packetConvert(packet: Packet): T

    @Throws(Exception::class)
    override fun subscribe(e: ObservableEmitter<T>) {
        TbitBle.commonCommand(commandId, key, value, { resultCode ->
            Log.i("ddd", "onResult: " + resultCode)
            if (!e.isDisposed) {
                if (resultCode != ResultCode.SUCCEED) {
                    e.onError(BleError(resultCode, errorMsg))
                }
            }
        }, { packet ->
            Log.i("ddd", "onPacketReceived: $packet")
            if (!e.isDisposed) {
                e.onNext(packetConvert(packet))
                e.onComplete()
            }
        })
    }
}