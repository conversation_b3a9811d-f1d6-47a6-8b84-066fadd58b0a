package com.tbit.installhelper.ble

import android.util.Log
import com.tbit.tbitblesdk.Bike.ResultCode
import com.tbit.tbitblesdk.Bike.TbitBle
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe

/**
 * Created by yankaibang on 2019/4/24.
 */
class BleLockOnSubscribe : ObservableOnSubscribe<Int> {

    @Throws(Exception::class)
    override fun subscribe(e: ObservableEmitter<Int>) {
        TbitBle.lock { resultCode ->
            Log.i("ddd", "onResult: " + resultCode)
            if (!e.isDisposed) {
                if (resultCode == ResultCode.SUCCEED) {
                    e.onNext(resultCode)
                    e.onComplete()
                } else {
                    e.onError(BleError(resultCode, "ble lock failed"))
                }
            }
        }
    }
}