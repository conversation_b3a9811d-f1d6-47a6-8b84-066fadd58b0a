package com.tbit.installhelper.ble

import android.bluetooth.BluetoothDevice
import com.tbit.tbitblesdk.Bike.util.BikeUtil

class BleFilter(val machineNO: String) {

    private val encryptDeviceId = hexString2Bytes("${BikeUtil.encryptStr(machineNO)}F") ?: ByteArray(0)

    fun check(bluetoothDevice: BluetoothDevice, i: Int, bytes: ByteArray): <PERSON><PERSON>an {
        return isSubArray(bytes, encryptDeviceId)
    }

    private fun isSubArray(array: ByteArray, subArray: ByteArray): Bo<PERSON>an {
        if (array.size < subArray.size) return false
        for (i in 0..array.size - subArray.size) {
            if (equals(array, i, subArray, 0, subArray.size)) {
                return true
            }
        }
        return false
    }

    private fun equals(a1: ByteArray, a1Pos: Int,
                       a2: ByteArray, a2Pos: Int,
                       length: Int): Bo<PERSON>an {
        for (i in 0 until length) {
            if (a1[a1Pos + i] != a2[a2Pos + i]) {
                return false
            }
        }
        return true
    }

    /**
     * hexString转byteArr
     *
     * 例如：
     * hexString2Bytes("00A8") returns { 0, (byte) 0xA8 }
     *
     * @param hexString 十六进制字符串
     * @return 字节数组
     */
    fun hexString2Bytes(hexString: String): ByteArray? {
        var hexString = hexString
        if (isSpace(hexString)) return null
        var len = hexString.length
        if (len % 2 != 0) {
            hexString = "0$hexString"
            len = len + 1
        }
        val hexBytes = hexString.toUpperCase().toCharArray()
        val ret = ByteArray(len shr 1)
        var i = 0
        while (i < len) {
            ret[i shr 1] = (hex2Dec(hexBytes[i]) shl 4 or hex2Dec(hexBytes[i + 1])).toByte()
            i += 2
        }
        return ret
    }

    /**
     * hexChar转int
     *
     * @param hexChar hex单个字节
     * @return 0..15
     */
    private fun hex2Dec(hexChar: Char): Int {
        return if (hexChar >= '0' && hexChar <= '9') {
            hexChar - '0'
        } else if (hexChar >= 'A' && hexChar <= 'F') {
            hexChar - 'A' + 10
        } else {
            throw IllegalArgumentException()
        }
    }

    /**
     * 判断字符串是否为null或全为空白字符
     *
     * @param s 待校验字符串
     * @return `true`: null或全空白字符<br></br> `false`: 不为null且不全空白字符
     */
    private fun isSpace(s: String?): Boolean {
        if (s == null) return true
        var i = 0
        val len = s.length
        while (i < len) {
            if (!Character.isWhitespace(s[i])) {
                return false
            }
            ++i
        }
        return true
    }
}