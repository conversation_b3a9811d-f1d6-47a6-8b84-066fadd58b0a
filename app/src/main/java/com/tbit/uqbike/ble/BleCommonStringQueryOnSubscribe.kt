package com.tbit.installhelper.ble

import com.tbit.iottest.entity.BleStringQueryResult

class BleCommonStringQueryOnSubscribe(vararg params: String): BleStringQueryOnSubscribe<BleStringQueryResult>() {

    override val stringValue: String = params.joinToString(";")

    override val errorMsg = "ble query failed"

    override fun onGetValue(result: BleStringQueryResult): BleStringQueryResult {
        return result
    }
}