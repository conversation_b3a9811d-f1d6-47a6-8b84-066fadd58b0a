package com.tbit.installhelper.ble

import android.util.Log
import com.tbit.tbitblesdk.Bike.ResultCode
import com.tbit.tbitblesdk.Bike.TbitBle
import com.tbit.tbitblesdk.Bike.services.command.callback.SimpleCommonCallback
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe

/**
 * Created by yankaibang on 2019/4/24.
 */
abstract class BleCommonSetOnSubscribe : ObservableOnSubscribe<Int> {

    abstract protected val commandId: Byte
    abstract protected val key: Byte
    abstract protected val value: Array<Byte>?
    abstract protected val errorMsg: String?

    @Throws(Exception::class)
    override fun subscribe(e: ObservableEmitter<Int>) {
        TbitBle.commonCommand(commandId, key, value, SimpleCommonCallback{ resultCode ->
            Log.i("ddd", "onResult: " + resultCode)
            if (!e.isDisposed) {
                if (resultCode == ResultCode.SUCCEED) {
                    e.onNext(resultCode)
                    e.onComplete()
                } else {
                    e.onError(BleError(resultCode, errorMsg))
                }
            }
        })
    }
}