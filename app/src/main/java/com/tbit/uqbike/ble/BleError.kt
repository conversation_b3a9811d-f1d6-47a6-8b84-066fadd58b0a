package com.tbit.installhelper.ble

import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbitblesdk.Bike.ResultCode
import com.tbit.uqbike.R

/**
 * Created by yankaibang on 2019/4/24.
 */

class BleError(var errCode: Int, var errMsg: String?) : Exception("errCode: $errMsg errMsg: $errMsg") {

    private val context get() = ContextUtil.getContext()

    fun getErrType(): String {
        when (errCode) {
            ResultCode.BLE_NOT_OPENED -> return context.getString(R.string.ble_not_opened)
            ResultCode.BLE_NOT_SUPPORTED -> return context.getString(R.string.ble_not_supported)
            ResultCode.DEVICE_NOT_FOUNDED -> return context.getString(R.string.device_not_founded)
            ResultCode.MOTION_STATE -> return context.getString(R.string.motion_state)
            ResultCode.DISCONNECTED -> return context.getString(R.string.disconnected)
            ResultCode.TIMEOUT -> return context.getString(R.string.timeout)
            ResultCode.ILLEGAL_COMMAND -> return context.getString(R.string.illegal_command)
            ResultCode.CONNECT_FAILED_ILLEGAL_KEY -> return context.getString(R.string.illegal_key)
        }
        return "${context.getString(R.string.ble_op_failed)} $errCode"
    }
}


