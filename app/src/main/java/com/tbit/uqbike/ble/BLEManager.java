package com.tbit.uqbike.ble;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.lsxiao.apollo.core.Apollo;
import com.tbit.maintenance.config.Constant;
import com.tbit.maintenance.utils.ContextUtil;
import com.tbit.uqbike.utils.ClsUtils;
import com.tbit.uqbike.utils.TypeConversion;

import java.util.ArrayList;
import java.util.List;

/**
 * 1、扫描设备
 * 2、配对设备
 * 3、解除设备配对
 * 4、连接设备
 * 6、发现服务
 * 7、打开读写功能
 * 8、数据通讯（发送数据、接收数据）
 * 9、断开连接
 */
@SuppressLint("MissingPermission")
public class BLEManager {
    private static final String TAG = "BLEManager";

    private static final long MAX_CONNECT_TIME = 20000;  //连接超时时间10s
    private String SERVICE_UUID = "0783B03E-8535-B5A0-7140-A304D2495CB7";  //蓝牙通讯服务
    private String READ_UUID = "0783B03E-8535-B5A0-7140-A304D2495CB8";  //读特征
    private String WRITE_UUID = "0783B03E-8535-B5A0-7140-A304D2495CBA";  //写特征


    private Context mContext;
    private BluetoothManager bluetoothManager;
    private BluetoothAdapter bluetooth4Adapter;
    private BluetoothGatt mBluetoothGatt;  //当前连接的gatt
    private String serviceUUID,readUUID,writeUUID;
    private BluetoothGattService bluetoothGattService;   //服务
    private BluetoothGattCharacteristic readCharacteristic;  //读特征
    private BluetoothGattCharacteristic writeCharacteristic; //写特征
    public BluetoothDevice curConnDevice;  //当前连接的设备
    public boolean isConnectIng = false;  //是否正在连接中
    public boolean isConState = false;//是否连接成功

    private Handler mHandler = new Handler();

    public BLEManager() {
    }

    private static BLEManager bleManager  = null;
    public static BLEManager getBleIns(){
        if (bleManager == null){
            bleManager = new BLEManager();
            if (bleManager.initBle(ContextUtil.INSTANCE.getContext())) {
//                MyToastUtil.INSTANCE.toast("该设备不支持低功耗蓝牙");
            } else {
                if (bleManager.isEnable()) {
                    //去打开蓝牙
                    bleManager.openBluetooth(ContextUtil.INSTANCE.getContext(), false);
                }
            }
            return bleManager;
        }
        return bleManager;
    }
    /**
     * 初始化
     * @param context
     */
    public boolean initBle(Context context){
        mContext = context;
        SERVICE_UUID = SERVICE_UUID.toLowerCase();
        READ_UUID = READ_UUID.toLowerCase();
        WRITE_UUID = WRITE_UUID.toLowerCase();
        if(!checkBle(context)){
            return false;
        }else{
            return true;
        }
    }

    ////////////////////////////////////  扫描设备  ///////////////////////////////////////////////
    //扫描设备回调
//    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
//    private BluetoothAdapter.LeScanCallback leScanCallback = new BluetoothAdapter.LeScanCallback() {
//        @Override
//        public void onLeScan(BluetoothDevice bluetoothDevice, int rssi, byte[] bytes) {
//            //在onLeScan()回调中尽量做少的操作，可以将扫描到的设备扔到另一个线程中处理
//            if(bluetoothDevice == null)
//                return;
//
//            if(bluetoothDevice.getName() != null){
//                Log.d("1111",bluetoothDevice.getName() + "-->" + bluetoothDevice.getAddress());
//                if (no.equals(bluetoothDevice.getAddress())){
//                    stopDiscoveryDevice();
//                    curConnDevice = bluetoothDevice;
//                    connectBleDevice(mContext,curConnDevice,30000,SERVICE_UUID,READ_UUID,WRITE_UUID);
//                }
//            }else{
//                Log.d("1111","null" + "-->" + bluetoothDevice.getAddress());
//            }
//        }
//    };

    @SuppressLint("NewApi")
    private ScanCallback leScanCallback = new ScanCallback() {
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            super.onScanResult(callbackType, result);
            // 扫描到设备时的回调
            if(result.getDevice() == null)
                return;

            if(result.getDevice().getName() != null){
                Log.d("1111",result.getDevice().getName() + "-->" + result.getDevice().getAddress());
                if (no.equals(result.getDevice().getAddress())){
                    stopDiscoveryDevice();
                    curConnDevice = result.getDevice();
                    connectBleDevice(mContext,curConnDevice,50000,SERVICE_UUID,READ_UUID,WRITE_UUID);
                }
            }else{
                Log.d("1111","null" + "-->" + result.getDevice().getAddress());
            }
        }
        @Override
        public void onBatchScanResults(List<ScanResult> results) {
            super.onBatchScanResults(results);
            // 扫描结果批量回调
        }
        @Override
        public void onScanFailed(int errorCode) {
            super.onScanFailed(errorCode);
            // 扫描失败的回调
        }
    };
    public String no = "";//设备编号
    private String secretKey = "";//密钥
    public String dataKey = "";//命令字
    public String tokenData = "";//
    private Boolean isSendMsg = false;

    public void setSendMsg(Boolean sendMsg) {
        isSendMsg = sendMsg;
    }

    /**
     * 设置时间段 扫描设备
     * @param scanTime  扫描时间
     */
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void startDiscoveryDevice(String no, String secretKey, long scanTime,Boolean isSendMsg){
        this.no = no;
        this.secretKey = secretKey;
        this.isSendMsg = isSendMsg;
        if(bluetooth4Adapter == null){
            Log.e("1111","startDiscoveryDevice-->bluetooth4Adapter == null");
            return;
        }
        if (mBluetoothGatt != null){
            mBluetoothGatt.disconnect();
            mBluetoothGatt.close();
            mBluetoothGatt = null;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            Log.d("1111","开始扫描设备"+no);
//            bluetooth4Adapter.startLeScan(leScanCallback);
//            ScanFilter scanFilter = new ScanFilter.Builder().setServiceUuid(new ParcelUuid(UUID.fromString(SERVICE_UUID))).build();
            ScanFilter scanFilter = new ScanFilter.Builder().setDeviceAddress(no).build();
            List<ScanFilter> filters = new ArrayList<>();
            filters.add(scanFilter);
            ScanSettings scanSettings = new ScanSettings.Builder().setScanMode(ScanSettings.SCAN_MODE_LOW_POWER).build();
            bluetooth4Adapter.getBluetoothLeScanner().startScan(filters, scanSettings, leScanCallback);
//            bluetooth4Adapter.getBluetoothLeScanner().startScan(leScanCallback);

        }else{
            return;
        }
        //设定最长扫描时间
        mHandler.postDelayed(stopScanRunnable,scanTime);
    }

    private Runnable stopScanRunnable = new Runnable() {
        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        public void run() {
            //scanTime之后还没有扫描到设备，就停止扫描。
            stopDiscoveryDevice();
            if (mYComMandCallback != null){
                mYComMandCallback.onConnectback(false);
            }
        }
    };

    //////////////////////////////////////  停止扫描  /////////////////////////////////////////////
    /**
     * 停止扫描
     */
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void stopDiscoveryDevice(){
        mHandler.removeCallbacks(stopScanRunnable);
        if(bluetooth4Adapter == null){
            Log.e("1111","stopDiscoveryDevice-->bluetooth4Adapter == null");
            return;
        }
        if(leScanCallback == null){
            Log.e("1111","stopDiscoveryDevice-->leScanCallback == null");
            return;
        }
        Log.d("1111","停止扫描设备");
//        bluetooth4Adapter.stopLeScan(leScanCallback);
        bluetooth4Adapter.getBluetoothLeScanner().stopScan(leScanCallback);
    }


    /////////////////////////////////////  执行连接  //////////////////////////////////////////////
    //连接/通讯结果回调
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    private BluetoothGattCallback bluetoothGattCallback = new BluetoothGattCallback() {
        @Override
        public void onPhyUpdate(BluetoothGatt gatt, int txPhy, int rxPhy, int status) {
            super.onPhyUpdate(gatt, txPhy, rxPhy, status);
        }
        @Override
        public void onPhyRead(BluetoothGatt gatt, int txPhy, int rxPhy, int status) {
            super.onPhyRead(gatt, txPhy, rxPhy, status);
        }

        //连接状态回调-连接成功/断开连接
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            super.onConnectionStateChange(gatt, status, newState);
            switch(status){
                case BluetoothGatt.GATT_SUCCESS:
                    Log.w("1111","BluetoothGatt.GATT_SUCCESS");
                    break;
                case BluetoothGatt.GATT_FAILURE:
                    Log.w("1111","BluetoothGatt.GATT_FAILURE");
                    break;
                case BluetoothGatt.GATT_CONNECTION_CONGESTED:
                    Log.w("1111","BluetoothGatt.GATT_CONNECTION_CONGESTED");
                    break;
                case BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION:
                    Log.w("1111","BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION");
                    break;
                case BluetoothGatt.GATT_INSUFFICIENT_ENCRYPTION:
                    Log.w("1111","BluetoothGatt.GATT_INSUFFICIENT_ENCRYPTION");
                    break;
                case BluetoothGatt.GATT_INVALID_OFFSET:
                    Log.w("1111","BluetoothGatt.GATT_INVALID_OFFSET");
                    break;
                case BluetoothGatt.GATT_READ_NOT_PERMITTED:
                    Log.w("1111","BluetoothGatt.GATT_READ_NOT_PERMITTED");
                    break;
                case BluetoothGatt.GATT_REQUEST_NOT_SUPPORTED:
                    Log.w("1111","BluetoothGatt.GATT_REQUEST_NOT_SUPPORTED");
                    break;
            }

            BluetoothDevice bluetoothDevice = gatt.getDevice();
            Log.d("1111","连接的设备：" + bluetoothDevice.getName() + "  " + bluetoothDevice.getAddress());

            isConnectIng = false;
            //移除连接超时
            mHandler.removeCallbacks(connectOutTimeRunnable);

            if(newState == BluetoothGatt.STATE_CONNECTED){
                Log.w("1111","连接成功");
                //连接成功去发现服务
                gatt.discoverServices();
                //设置发现服务超时时间
                mHandler.postDelayed(serviceDiscoverOutTimeRunnable,MAX_CONNECT_TIME);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    Log.w("1111","requestMtu");
                    gatt.requestMtu(247);
                }else {
                    Log.w("1111","requestMtu000");
                }
            }else if(newState == BluetoothGatt.STATE_DISCONNECTED) {
//                if (mYComMandCallback != null){
//                    mYComMandCallback.onConnectback(false);
//                }
                curConnDevice = null;
                no = "";
                //清空系统缓存
                ClsUtils.refreshDeviceCache(gatt);
                Log.e("1111", "断开连接status:" + status);
                gatt.close();  //断开连接释放连接

                Apollo.emit(Constant.Event.BLE_CONFAIL_AUTO);

                if(status == 133){
                    gatt.close();
                    if (mYComMandCallback != null){
                        mYComMandCallback.onConnectback(false);
                    }
                    Log.e("1111","连接失败status：" + status + "  " + bluetoothDevice.getAddress());
                    //无法连接
                }else if(status == 62){
                    gatt.close();
                    Log.e("1111","连接成功服务未发现断开status:" + status);
                    //成功连接没有发现服务断开
                }else if(status == 0){
//                    if(onBleConnectListener != null){
//                        onBleConnectListener.onDisConnectSuccess(gatt,bluetoothDevice,status); //0正常断开 回调
//                    }
                }else if(status == 8){
                    //因为距离远或者电池无法供电断开连接
                    // 已经成功发现服务
                }else if(status == 34){
                }else {
                    //其它断开连接
                }
                mBluetoothGatt = null;
            }else if(newState == BluetoothGatt.STATE_CONNECTING){
                Log.d("1111","正在连接...");
            }else if(newState == BluetoothGatt.STATE_DISCONNECTING){
                Log.d("1111","正在断开...");
                curConnDevice = null;
                no = "";
            }
        }

        //发现服务
        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            super.onServicesDiscovered(gatt, status);
            //移除发现服务超时
            mHandler.removeCallbacks(serviceDiscoverOutTimeRunnable);
            Log.d("1111","移除发现服务超时");

            Log.d("1111","发现服务");

            //配置服务信息
            if(setupService(gatt,serviceUUID,readUUID,writeUUID)){
                //成功发现服务回调
            }else{
                //发现服务失败回调
            }
        }

        @Override
        public void onCharacteristicRead(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            super.onCharacteristicRead(gatt, characteristic, status);
            Log.d("1111","读status: " + status);
        }

        //向蓝牙设备写入数据结果回调
        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            super.onCharacteristicWrite(gatt, characteristic, status);

            if(characteristic.getValue() == null){
                Log.e("1111","characteristic.getValue() == null");
                return;
            }
            //将收到的字节数组转换成十六进制字符串
            String msg = TypeConversion.bytes2HexString(characteristic.getValue(),characteristic.getValue().length);
            if(status == BluetoothGatt.GATT_SUCCESS){
                //写入成功
                Log.w("1111","写入成功：" + msg);
            }else if(status == BluetoothGatt.GATT_FAILURE){
                //写入失败
                Log.e("1111","写入失败：" + msg);
            }else if(status == BluetoothGatt.GATT_WRITE_NOT_PERMITTED){
                //没有权限
                Log.e("1111","没有权限！");
            }
        }

        //读取蓝牙设备发出来的数据回调
        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            super.onCharacteristicChanged(gatt, characteristic);
            Log.w("1111","收到数据str:0000000000");
            //接收数据
            byte[] bytes = characteristic.getValue();
            Ridemsg = TypeConversion.bytes2HexString(bytes);
            Log.w("1111","收到数据str:" + TypeConversion.bytes2HexString(bytes,bytes.length));
        }

        @Override
        public void onDescriptorRead(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            super.onDescriptorRead(gatt, descriptor, status);
            Log.w("1111","onDescriptorRead");
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            super.onDescriptorWrite(gatt, descriptor, status);
            Log.w("1111","onDescriptorWrite");
        }

        @Override
        public void onReliableWriteCompleted(BluetoothGatt gatt, int status) {
            super.onReliableWriteCompleted(gatt, status);
            Log.d("1111","onReliableWriteCompleted");
        }

        @Override
        public void onReadRemoteRssi(BluetoothGatt gatt, int rssi, int status) {
            super.onReadRemoteRssi(gatt, rssi, status);
            if(status == BluetoothGatt.GATT_SUCCESS){
                Log.w("1111","读取RSSI值成功，RSSI值：" + rssi + ",status" + status);
            }else if(status == BluetoothGatt.GATT_FAILURE){
                Log.w("1111","读取RSSI值失败，status：" + status);
            }
        }

        //修改MTU值结果回调
        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            super.onMtuChanged(gatt, mtu, status);
            ///设置mtu值，即bluetoothGatt.requestMtu()时触发，提示该操作是否成功
            if(status == BluetoothGatt.GATT_SUCCESS){  //设置MTU成功
                //MTU默认取的是23，当收到 onMtuChanged 后，会根据传递的值修改MTU，注意由于传输用掉3字节，因此传递的值需要减3。
                //mtu - 3
                Log.w("1111","设置MTU成功，新的MTU值：" + (mtu-3) + ",status" + status);

            }else if(status == BluetoothGatt.GATT_FAILURE){  //设置MTU失败
                Log.e("1111","设置MTU值失败：" + (mtu-3) + ",status" + status);
            }else {
                Log.e("1111","设置MTU值失败1：" + (mtu-3) + ",status" + status);
            }

        }
    };

    /**
     * 通过蓝牙设备连接
     * @param context  上下文
     * @param bluetoothDevice  蓝牙设备
     * @param outTime          连接超时时间
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public BluetoothGatt connectBleDevice(Context context, BluetoothDevice bluetoothDevice, long outTime,String serviceUUID,String readUUID,String writeUUID){
        if(bluetoothDevice == null){
            Log.e("1111","connectBleDevice()-->bluetoothDevice == null");
            if (mYComMandCallback != null){
                mYComMandCallback.onConnectback(false);
            }
            return null;
        }
        if(isConnectIng){
            Log.e("1111","connectBleDevice()-->isConnectIng = true");
            return null;
        }

        this.serviceUUID = serviceUUID;
        this.readUUID = readUUID;
        this.writeUUID = writeUUID;
//        this.onBleConnectListener = onBleConnectListener;

        this.curConnDevice = bluetoothDevice;

        Log.d("1111","开始准备连接：" + bluetoothDevice.getName() + "-->" + bluetoothDevice.getAddress());
        //出现 BluetoothGatt.android.os.DeadObjectException 蓝牙没有打开
        try{
            mBluetoothGatt = bluetoothDevice.connectGatt(context,false,bluetoothGattCallback);
//            if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
//                mBluetoothGatt = bluetoothDevice.connectGatt(context,false,bluetoothGattCallback,
//                        BluetoothDevice.TRANSPORT_LE,BluetoothDevice.PHY_LE_1M_MASK);
//            }else if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){
//                mBluetoothGatt = bluetoothDevice.connectGatt(context,false,bluetoothGattCallback,
//                        BluetoothDevice.TRANSPORT_LE);
//            }else {
//                mBluetoothGatt = bluetoothDevice.connectGatt(context,false,bluetoothGattCallback);
//            }
            mBluetoothGatt.connect();
            isConnectIng = true;

        }catch(Exception e){
            Log.e("1111","e:" + e.getMessage());
            if (mYComMandCallback != null){
                mYComMandCallback.onConnectback(false);
            }
        }

        //设置连接超时时间10s
        mHandler.postDelayed(connectOutTimeRunnable,outTime);

        return mBluetoothGatt;
    }

    //连接超时
    private Runnable connectOutTimeRunnable = new Runnable() {
        @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
        @Override
        public void run() {
            if(mBluetoothGatt == null){
                Log.e("1111","connectOuttimeRunnable-->mBluetoothGatt == null");
                return;
            }

            isConnectIng = false;
            isConState = false;
            mBluetoothGatt.disconnect();

            if (mYComMandCallback != null){
                mYComMandCallback.onConnectback(false);
            }
            //连接超时当作连接失败回调
//            if(onBleConnectListener != null){
//                onBleConnectListener.onConnectFailure(mBluetoothGatt,curConnDevice,"连接超时！",-1);  //连接失败回调
//            }
        }
    };

    //发现服务超时
    private Runnable serviceDiscoverOutTimeRunnable = new Runnable() {
        @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
        @Override
        public void run() {
            if(mBluetoothGatt == null){
                Log.e("1111","connectOuttimeRunnable-->mBluetoothGatt == null");
                return;
            }

            isConnectIng = false;
            isConState = false;
            mBluetoothGatt.disconnect();

            //发现服务超时当作连接失败回调
//            if(onBleConnectListener != null){
//                onBleConnectListener.onConnectFailure(mBluetoothGatt,curConnDevice,"发现服务超时！",-1);  //连接失败回调
//            }
        }
    };

    /**
     * 获取特定服务及特征
     * 1个serviceUUID -- 1个readUUID -- 1个writeUUID
     * @param bluetoothGatt
     * @param serviceUUID
     * @param readUUID
     * @param writeUUID
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    private boolean setupService(BluetoothGatt bluetoothGatt,String serviceUUID,String readUUID,String writeUUID) {
        if (bluetoothGatt == null) {
            Log.e("1111", "setupService()-->bluetoothGatt == null");
            return false;
        }

        if(serviceUUID == null){
            Log.e("1111", "setupService()-->serviceUUID == null");
            return false;
        }

        for (BluetoothGattService service : bluetoothGatt.getServices()) {
            Log.d("1111", "service ======= " + service.getUuid().toString());
            if (service.getUuid().toString().equals(serviceUUID)) {
                bluetoothGattService = service;
            }
        }
        //通过上面方法获取bluetoothGattService
//        bluetoothGattService = bleManager.getBluetoothGattService(bluetoothGatt,ConsData.MY_BLUETOOTH4_UUID);
        if (bluetoothGattService == null) {
            //找不到该服务就立即断开连接
            Log.e("1111", "setupService()-->bluetoothGattService == null");
            return false;
        }
        Log.d("1111", "setupService()-->bluetoothGattService = " + bluetoothGattService.toString());

        if(readUUID == null || writeUUID == null){
            Log.e("1111", "setupService()-->readUUID == null || writeUUID == null");
            return false;
        }

        for (BluetoothGattCharacteristic characteristic : bluetoothGattService.getCharacteristics()) {
            if (characteristic.getUuid().toString().equals(readUUID)) {  //读特征
                readCharacteristic = characteristic;
            } else if (characteristic.getUuid().toString().equals(writeUUID)) {  //写特征
                writeCharacteristic = characteristic;
            }
        }
        if (readCharacteristic == null) {
            Log.e("1111", "setupService()-->readCharacteristic == null");
            return false;
        }
        if (writeCharacteristic == null) {
            Log.e("1111", "setupService()-->writeCharacteristic == null");
            return false;
        }
        //打开读通知
        enableNotification(true, bluetoothGatt, readCharacteristic);

        //重点中重点，需要重新设置
//        List<BluetoothGattDescriptor> descriptors = writeCharacteristic.getDescriptors();
        List<BluetoothGattDescriptor> descriptors = readCharacteristic.getDescriptors();
        for (BluetoothGattDescriptor descriptor : descriptors) {
            descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
            bluetoothGatt.writeDescriptor(descriptor);
        }
        Apollo.emit(Constant.Event.BLE_CONSUC);
        //延迟2s，保证所有通知都能及时打开
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                isConState = true;
                if (isSendMsg){
                    sendMessage(XiaoAnCommand.getCommand(dataKey,tokenData),true,true);
                }
            }
        }, 1000);


        return true;

    }

    /////////////////////////////////////////  打开通知  //////////////////////////////////////////

    /**
     * 设置读特征接收通知
     * @param enable  为true打开通知
     * @param gatt    连接
     * @param characteristic  特征
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public void enableNotification(boolean enable, BluetoothGatt gatt, BluetoothGattCharacteristic characteristic){
        if(gatt == null){
            Log.e("1111","enableNotification-->gatt == null");
            return;
        }
        if(characteristic == null){
            Log.e("1111","enableNotification-->characteristic == null");
            return;
        }
        //这一步必须要有，否则接收不到通知
        gatt.setCharacteristicNotification(characteristic,enable);
    }


    ///////////////////////////////////  发送数据  ///////////////////////////////////////////////
    private String Ridemsg = "";
    /**
     * 发送消息  byte[]数组
     * @param msg  消息
     * @return  true  false
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public boolean sendMessage(byte[] msg,boolean isCallBack,Boolean isSendMsg){
        Ridemsg = "";
        this.isSendMsg = isSendMsg;
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mYComMandCallback != null){
                    if (isCallBack){
                        mYComMandCallback.onCallback(Ridemsg);
                    }
                }
            }
        }, 1000);

        if(writeCharacteristic == null){
            Log.e("1111","sendMessage(byte[])-->writeGattCharacteristic == null");
            return false;
        }

        if(mBluetoothGatt == null){
            Log.e("1111","sendMessage(byte[])-->mBluetoothGatt == null");
            return false;
        }

//        boolean  b = writeCharacteristic.setValue(TypeConversion.hexString2Bytes(msg));

        boolean  b = writeCharacteristic.setValue(msg);
        Log.d("1111", "写特征设置值结果：" + b);

        return mBluetoothGatt.writeCharacteristic(writeCharacteristic);
    }

    private ComMandCallback mYComMandCallback;

    public void setmYComMandCallback(ComMandCallback mYComMandCallback) {
        this.mYComMandCallback = mYComMandCallback;
    }

    public interface ComMandCallback {
        void onCallback(String RideData);
        void onConnectback(boolean isSuc);
    }

    ///////////////////////////////////  断开连接  ///////////////////////////////////////////////
    /**
     * 断开连接
     */
    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
    public void disConnectDevice(){
        isConState = false;
        if(mBluetoothGatt == null){
            Log.e("1111","disConnectDevice-->bluetoothGatt == null");
            return;
        }
        //系统断开
        mBluetoothGatt.disconnect();
        //close()方法应该放在断开回调处，放在此处，会没有回调信息
//        mBluetoothGatt.close();
//        mBluetoothGatt = null;
    }



    /**
     * 检测手机是否支持4.0蓝牙
     * @param context  上下文
     * @return true--支持4.0  false--不支持4.0
     */
    private boolean checkBle(Context context){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {  //API 18 Android 4.3
            bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
            if(bluetoothManager == null){
                return false;
            }
            bluetooth4Adapter = bluetoothManager.getAdapter();  //BLUETOOTH权限
            if(bluetooth4Adapter == null){
                return false;
            }else{
                Log.d("1111","该设备支持蓝牙4.0");
                return true;
            }
        }else{
            return false;
        }
    }

    /**
     * 获取蓝牙状态
     */
    public boolean isEnable(){
        if(bluetooth4Adapter == null){
            return false;
        }
        return bluetooth4Adapter.isEnabled();
    }

    /**
     * 打开蓝牙
     * @param isFast  true 直接打开蓝牙  false 提示用户打开
     */
    public void openBluetooth(Context context,boolean isFast){
        if(!isEnable()){
            if(isFast){
                Log.d("1111","直接打开手机蓝牙");
                bluetooth4Adapter.enable();  //BLUETOOTH_ADMIN权限
            }else{
                Log.d("1111","提示用户去打开手机蓝牙");
                Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                context.startActivity(enableBtIntent);
            }
        }else{
            Log.d("1111","手机蓝牙状态已开");
        }
    }

    /**
     * 直接关闭蓝牙
     */
    public void closeBluetooth(){
        if(bluetooth4Adapter == null)
            return;

        bluetooth4Adapter.disable();
    }


    /**
     * 本地蓝牙是否处于正在扫描状态
     * @return true false
     */
    public boolean isDiscovery(){
        if(bluetooth4Adapter ==null){
            return false;
        }
        return bluetooth4Adapter.isDiscovering();
    }
}
