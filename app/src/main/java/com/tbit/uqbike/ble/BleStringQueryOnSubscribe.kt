package com.tbit.installhelper.ble

import com.tbit.iottest.entity.BleStringQueryResult
import com.tbit.tbitblesdk.bluetooth.util.ByteUtil
import com.tbit.tbitblesdk.protocol.Packet
import java.nio.charset.Charset

/**
 * Created by yankaibang on 2019/5/20.
 */
abstract class BleStringQueryOnSubscribe<T> : BleCommonQueryOnSubscribe<T>() {

    private val charset = Charset.forName("ascii")
    override val commandId = 0x04.toByte()
    override val key = 0xfd.toByte()
    override val value get() = stringValue.toByteArray(charset).toTypedArray()
    abstract val stringValue: String
    abstract fun onGetValue(results: BleStringQueryResult): T

    override fun packetConvert(packet: Packet): T {
        val keyValues = parseKeyValues(getFirstValue(packet))
        return onGetValue(keyValues)
    }

    private fun getFirstValue(packet: Packet): ByteArray? {
        val data = packet.packetValue.data
        return if (!data.isEmpty()) {
            ByteUtil.byteArrayToUnBoxed(data[0].value)
        } else null
    }

    private fun parseKeyValues(value: ByteArray?): BleStringQueryResult {
        val response = value?.toString(charset)
            ?: return BleStringQueryResult(null, emptyMap(), emptyList())
        val successIndex = response.indexOf("Y:")
        val failedIndex = response.indexOf("N:")
        val indexList = listOf(successIndex, failedIndex, response.length).filter { it >= 0 }.sorted()
        val successResponse = (0 until indexList.size - 1).firstOrNull { indexList[it] == successIndex }
            ?.let { response.substring(indexList[it] + 2, indexList[it + 1]) } ?: ""
        val failedResponse = (0 until indexList.size - 1).firstOrNull { indexList[it] == failedIndex }
            ?.let { response.substring(indexList[it] + 2, indexList[it + 1]) } ?: ""
        val successes = parseKeyValue(successResponse)
        val failed = failedResponse.split(",").map { it.trim().toUpperCase() }
        return BleStringQueryResult(response, successes.toMap(), failed)
    }

    private fun parseKeyValue(response: String): List<Pair<String, String>> {
        val splits = response.split(",")
        val keyValues = mutableListOf<String>()
        val extraValues = mutableListOf<String>()
        var tempKeyValue: String? = null

        splits.forEach {
            if (it.contains("=")) {
                tempKeyValue?.let {
                    extraValues.add(0, it)
                    keyValues.add(extraValues.joinToString(","))
                }

                extraValues.clear()
                tempKeyValue = it
            } else {
                extraValues.add(it)
            }
        }

        tempKeyValue?.let {
            extraValues.add(0, it)
            keyValues.add(extraValues.joinToString(","))
        }

        return keyValues.map { it.split("=") }
            .filter { it.size == 2 }.map { it[0].trim().toUpperCase() to it[1].trim() }
    }
}