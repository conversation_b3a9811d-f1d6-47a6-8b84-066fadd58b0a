package com.tbit.installhelper.ble

import com.tbit.iottest.entity.BleStringSetResult

class BleCommonStringSetOnSubscribe(override val stringValue: String): BleStringSetOnSubscribe<BleStringSetResult>() {

    override val errorMsg = "ble set failed"

    constructor(vararg keyValues: Pair<String, String>)
            : this(keyValues.joinToString(";") { "${it.first}=${it.second}" })

    override fun onGetValue(result: BleStringSetResult): BleStringSetResult {
        return result
    }
}