package com.tbit.installhelper.ble

import com.tbit.iottest.entity.BleStringSetResult
import com.tbit.tbitblesdk.bluetooth.util.ByteUtil
import com.tbit.tbitblesdk.protocol.Packet
import java.nio.charset.Charset

/**
 * Created by yankaibang on 2019/5/20.
 */
abstract class BleStringSetOnSubscribe<T> : BleCommonQueryOnSubscribe<T>() {

    private val charset = Charset.forName("ascii")
    override val commandId = 0x03.toByte()
    override val key = 0xfd.toByte()
    override val value get() = stringValue.toByteArray(charset).toTypedArray()
    abstract val stringValue: String
    abstract fun onGetValue(result: BleStringSetResult): T

    override fun packetConvert(packet: Packet): T {
        val result = parseKeys(getFirstValue(packet))
        return onGetValue(result)
    }

    private fun getFirstValue(packet: Packet): ByteArray? {
        val data = packet.packetValue.data
        return if (!data.isEmpty()) {
            ByteUtil.byteArrayToUnBoxed(data[0].value)
        } else null
    }

    private fun parseKeys(value: ByteArray?): BleStringSetResult {
        val response = value?.toString(charset)
            ?: return BleStringSetResult(null, emptyList(), emptyList())
        val successIndex = response.indexOf("Y:")
        val failedIndex = response.indexOf("N:")
        val indexList = listOf(successIndex, failedIndex, response.length).filter { it >= 0 }.sorted()
        val successResponse = (0 until indexList.size - 1).firstOrNull { indexList[it] == successIndex }
            ?.let { response.substring(indexList[it] + 2, indexList[it + 1]) }
        val failedResponse = (0 until indexList.size - 1).firstOrNull { indexList[it] == failedIndex }
            ?.let { response.substring(indexList[it] + 2, indexList[it + 1]) }
        val successes = successResponse?.split(",")?.map { it.trim().toUpperCase() } ?: emptyList()
        val failed = failedResponse?.split(",")?.map { it.trim().toUpperCase() } ?: emptyList()
        return BleStringSetResult(response, successes, failed)
    }
}