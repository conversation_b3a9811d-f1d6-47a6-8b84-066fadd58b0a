package com.tbit.installhelper.ble

import android.util.Log
import com.tbit.tbitblesdk.Bike.ResultCode
import com.tbit.tbitblesdk.Bike.TbitBle
import com.tbit.tbitblesdk.Bike.model.BikeState
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe

/**
 * Created by yankaibang on 2019/4/24.
 */
class BleUpdateOnSubscribe : ObservableOnSubscribe<BikeState> {

    @Throws(Exception::class)
    override fun subscribe(e: ObservableEmitter<BikeState>) {
        TbitBle.update(
            { resultCode ->
                Log.i("ddd", "onResult: " + resultCode)
                if (!e.isDisposed) {
                    if (resultCode != ResultCode.SUCCEED) {
                        e.onError(BleError(resultCode, "ble update failed"))
                    }
                }
            }, { bikeState ->
                if (!e.isDisposed) {
                    e.onNext(bikeState)
                    e.onComplete()
                }
            }
        )
    }
}