package com.tbit.uqbike.ble

import com.lsxiao.apollo.core.Apollo
import com.tbit.installhelper.ble.BleLockHelmetOnSubscribe
import com.tbit.installhelper.ble.BleUnlockHelmetOnSubscribe
import com.tbit.maintenance.config.Constant
import com.tbit.tbitblesdk.Bike.ResultCode
import com.tbit.tbitblesdk.Bike.TbitBle
import com.tbit.tbitblesdk.Bike.services.command.callback.SimpleCommonCallback
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers

object BleModelNew {
    var isConing = false//是否连接中
    var isCon = false //是否连接成功
    var isLockAuto = false// 连接成功是否自动下发锁车
    var conRestartTime = 0;//重连 次数
    var writeRestartTime = 0;//重写命令 次数
    /**
     * 连接
     * @param machineNO 设备编号
     * @param secretKey 连接秘钥
     */
    fun connect(machineNO: String, secretKey: String,isLock : Boolean): Int {
        this.isLockAuto = isLock
        var conCode = 0
        isConing = true
        TbitBle.connect(machineNO, secretKey,
            { resultCode ->
                isConing = false
                MyLogUtil.Log("1111","onResult: " + resultCode)
//                Log.i("ddd", "onResult: " + resultCode)
                if (resultCode == ResultCode.SUCCEED) {
                    Apollo.emit(Constant.Event.BLE_CONSUC)
                    conRestartTime = 0
                    isCon = true
                    conCode = resultCode
                    if(isLockAuto){
                        lock()
                    }
                } else {
                    conRestartTime = conRestartTime + 1
                    if (conRestartTime > 2){
                        conCode = resultCode
                        Apollo.emit(Constant.Event.BLE_CONFAIL)
                        TbitBle.stopScan()
                        conRestartTime = 0
                    }else{
                        connect(machineNO,secretKey,isLock)
                    }
                }
            }
        ) { _ -> }
        return conCode
    }
    /**
     * 断开连接
     */
    fun disconnect(){
        TbitBle.disConnect()
        conRestartTime = 0
        isLockAuto = false
        isConing = false
        isCon = false
    }

    /**
     * 获取设备状态
     */
    fun getBikeState(){
        TbitBle.update(
            { resultCode ->
                MyLogUtil.Log("1111","onResult: " + resultCode)
            }, { bikeState -> }
        )
    }

    /**
     * 关锁
     */
    fun lock(){
        TbitBle.lock { resultCode ->
            MyLogUtil.Log("1111","onResult: " + resultCode)
            if (resultCode == 0){
                Apollo.emit(Constant.Event.BLE_CLOSECARSUC)
                writeRestartTime = 0
            }else{
                writeRestartTime = writeRestartTime + 1
                if (writeRestartTime > 2){
                    writeRestartTime = 0
                    Apollo.emit(Constant.Event.BLE_CLOSECARFAIL)
                }else{
                    lock()
                }
            }
        }
    }

    /**
     * 解锁
     */
    fun unlock(){
        TbitBle.unlock { resultCode ->
            MyLogUtil.Log("1111","onResult: " + resultCode)
        }
    }

    /**
     * 临时停车
     */
    fun tempStop(){
        val commandId = 0x03.toByte()
        val key = 0x01.toByte()
        val value =  arrayOf(0x30.toByte())
        val errorMsg = "ble temp stop failed"
        TbitBle.commonCommand(commandId, key, value, SimpleCommonCallback{ resultCode ->
            MyLogUtil.Log("1111","onResult: " + resultCode)
        })
    }

    /**
     * 继续用车
     */
    fun continueRide(){
        val commandId = 0x03.toByte()
        val key = 0x01.toByte()
        val value =  arrayOf(0x31.toByte())
        val errorMsg = "ble continue ride failed"
        TbitBle.commonCommand(commandId, key, value, SimpleCommonCallback{ resultCode ->
            MyLogUtil.Log("1111","onResult: " + resultCode)
        })
    }

    /**
     * 开头盔锁
     */
    fun unlockHelmet(): Observable<Int> {
        return Observable.create(BleUnlockHelmetOnSubscribe())
            .subscribeOn(AndroidSchedulers.mainThread())
            .observeOn(AndroidSchedulers.mainThread())
            .map { Constant.CommandState.STATE_EXECUTE_SUCCESS }
    }

    /**
     * 关头盔锁
     */
    fun lockHelmet(): Observable<Int> {
        return Observable.create(BleLockHelmetOnSubscribe())
            .subscribeOn(AndroidSchedulers.mainThread())
            .observeOn(AndroidSchedulers.mainThread())
            .map { Constant.CommandState.STATE_EXECUTE_SUCCESS }
    }
}