package com.tbit.uqbike.dialog.util

import android.app.Activity
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.core.app.ActivityCompat.startActivityForResult
import com.baidu.ar.no
import com.google.gson.Gson
import com.stripe.android.model.ConsumerPaymentDetails.Card.Companion.type
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.MineActivity
import com.tbit.uqbike.activity.RideCardNewActivity
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.PowerPayDialog
import com.tbit.uqbike.entity.RideCardNumData
import com.tbit.uqbike.entity.powerAmoutData
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.startActivityForResult

object RidingDialogUtil {
    val TYPE_POWER_1 = 1 //正常换电
    val TYPE_POWER_2 = 2 //异P换电
    val TYPE_POWER_3 = 3 //收费换电
    fun showPowerDialog(activity : BaseActivity,orderNo : String,type : Int,num : Int,amount : String,powerData : powerAmoutData,onNotify : (data: String) -> Unit = {}){
        var unit = ResUtil.getString(R.string.s_power_dig_unit1)
        var content = ""
        var spannableString : SpannableString? = null
        var rightCont = ResUtil.getString(R.string.ok)
        when(type){
            TYPE_POWER_1 ->{
                content = ResUtil.getString(R.string.s_power_dig_title)+"\n"+ ResUtil.getString(R.string.s_power_dig_con1)
            }
            TYPE_POWER_2 ->{
                content = ResUtil.getString(R.string.s_power_dig_title)+"\n"+ ResUtil.getString(R.string.s_power_dig_con21)+"\n"+
                        ResUtil.getString(R.string.s_power_con2,num.toString(),amount+Glob.CurrencyUnit)
                rightCont = ResUtil.getString(R.string.s_gopay)
            }
            TYPE_POWER_3 ->{
                content = ResUtil.getString(R.string.s_power_dig_title)+"\n"+ ResUtil.getString(R.string.s_power_dig_con21)+"\n"+
                        ResUtil.getString(R.string.s_power_dig_con3,amount+Glob.CurrencyUnit)
                rightCont = ResUtil.getString(R.string.s_gopay)
            }
        }
        spannableString = SpannableString(content)
//        var data1 = spannableString.split(unit)[0].length
//        spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_ED0000)), data1, data1+unit.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        CommDialog.Builder(activity).setTitle(ResUtil.getString(R.string.dialog_tip)).setSpanContent(spannableString!!)
            .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(rightCont).
            setConLeft(true).setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() {}
                override fun rightClick() {
//                    onNotify("sure")
                    when(type){
                        TYPE_POWER_1 ->{
                            activity.loadingDialogHelper!!.show {  }
                            sumitPower(activity,orderNo,onNotify = {
                                if (it.equals(Constant.SUC)){
                                    onNotify(Constant.SUC)
                                }
                            })
                        }
                        TYPE_POWER_2 ->{
                            var powerPayDialog = PowerPayDialog(activity,activity.loadingDialogHelper,orderNo,powerData)
                            activity.lifecycleDialogHelper.show(powerPayDialog)
                        }
                        TYPE_POWER_3 ->{
                            var powerPayDialog = PowerPayDialog(activity,activity.loadingDialogHelper,orderNo,powerData)
                            activity.lifecycleDialogHelper.show(powerPayDialog)
                        }
                    }
                }
            }).build().show()
    }
    fun sumitPower(activity : BaseActivity, orderNo : String,onNotify : (data: String) -> Unit = {}){
        OrderModel.sumitPower(orderNo).subscribeBy(
            onNext = {
                activity.loadingDialogHelper!!.dismiss()
                MyLogUtil.Log("1111","===提交 换电服务信息=="+it.toString())
                onNotify(Constant.SUC)
//                val resultData = Gson().fromJson(it.toString(), powerAmoutData::class.java)
            },
            onError = {
                activity.loadingDialogHelper!!.dismiss()
            }
        ).toCancelable()
    }
    fun showRideCardDialog(activity : BaseActivity,onNotify : (data: String) -> Unit = {}){
        // Disable the loading dialog here to avoid flicker before showing the ride card prompt
        /*
        activity.loadingDialogHelper.show {  }
        */
        ComModel.getRideCard().subscribeBy(
            onNext = {
                activity.loadingDialogHelper.dismiss() // Keep dismiss call in case network takes time
                MyLogUtil.Log("1111","===获取骑行卡号 信息=="+it.toString())
                val resultData_card = Gson().fromJson(it.toString(), RideCardNumData::class.java)
                if (resultData_card.isShowAdv){
//                    if (resultData_card.ride_card_no.isNullOrEmpty()){
//
//                    }else{
//                        onNotify("")
//                    }
                    var unit = ResUtil.getString(R.string.s_dig_buycard)
                    var content = ResUtil.getString(R.string.s_dig_ucard)+"\n"+unit
                    var spannableString = SpannableString(content)
                    if (content.contains(unit.toString())){
                        var data1 = spannableString.split(unit)[0].length
                        spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.blue_namal)), data1, data1+unit.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }

                    CommDialog.Builder(activity).setTitle(ResUtil.getString(R.string.s_dialog_wxtx_tip)).setSpanContent(spannableString!!)
                        .setLeftText(ResUtil.getString(R.string.s_ridingnow)).setRightText(ResUtil.getString(R.string.s_gobuy)).setType("1").
                        setShowImg(R.drawable.ic_dig_card).setCanceledOnOutside(true)
                        .setClickListen(object : CommDialog.TwoSelDialog {
                            override fun leftClick() {
                                onNotify("")
                            }
                            override fun rightClick() {
                                activity.startActivity<RideCardNewActivity>()
                            }
                        }).build().show()
                }else{
                    onNotify("")
                }

            },
            onError = { activity.loadingDialogHelper.dismiss() } // Keep dismiss in onError
        ).toCancelable()



    }
}