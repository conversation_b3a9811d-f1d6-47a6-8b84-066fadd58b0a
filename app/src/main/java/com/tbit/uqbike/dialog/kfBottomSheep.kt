package com.tbit.uqbike.dialog

import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.FaultRecordActivity
import com.tbit.uqbike.activity.FeedbackFaultActivity
import com.tbit.uqbike.activity.LeaveMsgRecordActivity
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.activity.OrderCostRecordActivity
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.utils.MDUtil
import org.jetbrains.anko.support.v4.startActivity
import org.jetbrains.anko.support.v4.toast
import org.json.JSONObject

class kfBottomSheep(var act : BaseActivity): BaseSheetDialogFragment() {

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val layoutId = R.layout.dialog_kfbottom
        return inflater.inflate(layoutId, container, false)
    }

    var Myview : View? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Myview = view

        Myview?.findViewById<LinearLayout>(R.id.layout_cost)?.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("fee_appeal_click",properties)
            startActivity<OrderCostRecordActivity>()
        }

        Myview?.findViewById<LinearLayout>(R.id.layout_fault)?.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("fault_reporting_click",properties)
            startActivity<FeedbackFaultActivity>()
        }

        Myview?.findViewById<LinearLayout>(R.id.layout_leavemsg)?.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("message_click",properties)
            startActivity<LeaveMsgRecordActivity>()
        }

    }

    private fun tryLoginIfNot(): Boolean {
        val isLoggedIn = Glob.isLogin
        if (!isLoggedIn) {
            toast(R.string.login_first)
            FlavorConfig.appRoute.login()
        }
        return isLoggedIn
    }

    override fun onStart() {
        super.onStart()
        (Myview?.findViewById<LinearLayout>(R.id.root_view)?.parent as View).setBackgroundColor(
            Color.TRANSPARENT)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
        MDUtil.pageStar(eventName)
    }
    var eventName = "customer_service"
    override fun onDestroy() {
        super.onDestroy()
        val properties = JSONObject()
        properties.put("tag",eventName)
        MDUtil.pageEnd(eventName,properties)
    }
}