package com.tbit.uqbike.dialog

import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.annotation.NonNull
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.adapter.InviteAdapter
import com.tbit.uqbike.adapter.personEditAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.personItemSelData
import com.tbit.uqbike.roundview.RoundTextView
import org.jetbrains.anko.support.v4.dip

class personEditDialog (var act : BaseActivity,var listData : ArrayList<personItemSelData>): BaseSheetDialogFragment() {
    var onItemListener = {_: personItemSelData ->}
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val layoutId = R.layout.dialog_person_edit
        return inflater.inflate(layoutId, container, false)
    }

    private val adapter by lazy { personEditAdapter() }
    var Myview : View? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Myview = view
        Myview!!.post {
            val parent = Myview!!.parent as View
            val params = parent.layoutParams as CoordinatorLayout.LayoutParams
            val behavior = params.behavior
            val mBottomSheetBehavior = behavior as BottomSheetBehavior<*>?
            mBottomSheetBehavior?.isDraggable = false
        }

        Myview?.findViewById<RoundTextView>(R.id.rtv_cannel)?.setOnClickListener{
            dismissAllowingStateLoss()
        }
        Myview?.findViewById<RecyclerView>(R.id.rcv)?.layoutManager = LinearLayoutManager(
            ContextUtil.getContext())
        val spacing = dip(5)
        val spanCount = 1
        Myview?.findViewById<RecyclerView>(R.id.rcv)?.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        Myview?.findViewById<RecyclerView>(R.id.rcv)?.adapter = adapter

        adapter.source = listData
        adapter.notifyDataSetChanged()
        adapter.setOnItemClickListener {
            onItemListener(it)
        }
    }
    override fun onStart() {
        super.onStart()
        (Myview?.findViewById<LinearLayout>(R.id.root_view)?.parent as View).setBackgroundColor(
            Color.TRANSPARENT)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }
//        MDUtil.pageStar(eventName)
    }
    //    var eventName = "more_features"
    override fun onDestroy() {
        super.onDestroy()
//        val properties = JSONObject()
//        properties.put("tag",eventName)
//        MDUtil.pageEnd(eventName,properties)
    }
}