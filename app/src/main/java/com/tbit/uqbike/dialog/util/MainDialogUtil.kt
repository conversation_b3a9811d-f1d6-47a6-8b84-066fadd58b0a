package com.tbit.uqbike.dialog.util

import android.content.Intent
import android.provider.Settings
import androidx.core.app.ActivityCompat.startActivityForResult
import androidx.core.content.ContextCompat.startActivity
import com.baidu.ar.it
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.LifecycleDialogHelper
import com.tbit.maintenance.utils.LoadingDialogHelper
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.ChargeNewActivity
import com.tbit.uqbike.activity.HomeActivity
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.activity.RecordDetailActivity
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.CustomPhoneCallDialog
import com.tbit.uqbike.entity.HotLineData
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.json.JSONObject

object MainDialogUtil {
    fun GpsDig(act : MainActivity){
        if (Glob.isLogin){
            CommDialog.Builder(act).setTitle(ResUtil.getString(com.tbit.uqbike.R.string.dialog_tip)).setContent(act.getString(
                com.tbit.uqbike.R.string.str_gps_open_hint))
                .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(act.getString(
                    com.tbit.uqbike.R.string.s_opengo)).setCanceledOnOutside(true)
                .setClickListen(object : CommDialog.TwoSelDialog {
                    override fun leftClick() {}
                    override fun rightClick() {
                        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                        act.startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
                    }
                }).build().show()
        }
    }

    fun GpsDig(act : HomeActivity){
        if (Glob.isLogin){
            CommDialog.Builder(act).setTitle(ResUtil.getString(com.tbit.uqbike.R.string.dialog_tip)).setContent(act.getString(
                com.tbit.uqbike.R.string.str_gps_open_hint))
                .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(act.getString(
                    com.tbit.uqbike.R.string.s_opengo)).setCanceledOnOutside(true)
                .setClickListen(object : CommDialog.TwoSelDialog {
                    override fun leftClick() {}
                    override fun rightClick() {
                        val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                        act.startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
                    }
                }).build().show()
        }
    }

    fun BleDig(act : MainActivity){
        CommDialog.Builder(act).setTitle(act.getString(R.string.s_ble_title)).
        setContent(act.getString(R.string.s_ble_cont))
            .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(act.getString(com.tbit.uqbike.R.string.s_opengo)).setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() {}
                override fun rightClick() {
                    val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                    act.startActivity(intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK))
                }
            }).build().show()
    }

    fun RecordDetailDig(act : MainActivity,orderNo : String,isRental : Boolean){
        CommDialog.Builder(act).setTitle(ResUtil.getString(com.tbit.uqbike.R.string.dialog_tip)).setContent(act.getString(
            com.tbit.uqbike.R.string.s_have_nopayorder))
            .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(act.getString(
                com.tbit.uqbike.R.string.s_gopay)).setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() {}
                override fun rightClick() {
                    act.startActivity(RecordDetailActivity.createIntent(act,orderNo,false,isRental))
                }
            }).build().show()
    }

    fun MinAmountDig(act : BaseActivity,sys_minimum_amount : Float){
        CommDialog.Builder(act).setTitle(ResUtil.getString(
            com.tbit.uqbike.R.string.dialog_tip)).setContent(act.getString(com.tbit.uqbike.R.string.s_nusercar_hint, (AppUtil.getFloat2(sys_minimum_amount).toString()+Glob.CurrencyUnit)))
            .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(act.getString(com.tbit.uqbike.R.string.confirm_recharge)).setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() {}
                override fun rightClick() {
                    act.startActivity(ChargeNewActivity.createIntent(act, 0f,true))
                    val properties = JSONObject()
                    MDUtil.clickEvent("go_to_recharge_click",properties)
                }
            }).build().show()
    }

    /**
     * 客服弹框
     * @param isPower 是否换电
     */
    fun kfDig(loadingDialogHelper : LoadingDialogHelper,lifecycleDialogHelper : LifecycleDialogHelper,isPower : Boolean = false){
        loadingDialogHelper.show {  }
        val listKey = arrayOf("customer_service_number","line_id","facebook_url")
        ComModel.getConfig(listKey).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取客服电话 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), HotLineData::class.java)
                var phoneInfoList = ArrayList<PhoneInfo>()
                if (!resultData.customer_service_number.isNullOrEmpty()){
                    phoneInfoList.add(PhoneInfo(resultData.customer_service_number, Constant.CustomerServiceType.PHONE))
                }
                if (!resultData.line_id.isNullOrEmpty()){
                    phoneInfoList.add(PhoneInfo(resultData.line_id, Constant.CustomerServiceType.LINE))
                }
                if (!resultData.facebook_url.isNullOrEmpty()){
                    phoneInfoList.add(PhoneInfo(resultData.facebook_url, Constant.CustomerServiceType.FACEBOOK))
                }
                if (isPower){
                    lifecycleDialogHelper.show(CustomPhoneCallDialog.newInstance(phoneInfoList,true))
                }else{
                    lifecycleDialogHelper.show(CustomPhoneCallDialog.newInstance(phoneInfoList))
                }
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }
}