package com.tbit.maintenance.config

import android.os.Environment
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.uqbike.bean.AdDeposit
import java.io.File


/**
 * Created by yankaibang on 2018/10/8.
 */
object Constant {

    const val RETURN_BIKE_ACCURACY_LIMIT = 100
    const val DEFAULT_FORCE_READ_DURATION = 5 //默认强制阅读时长
    const val PAYPAY_AUTH_TIMEOUT = 10 //Paypay支付获取授权信息超时时间
    const val POWER_OUT_MAX_RECHARGE_TIME = 3 //车辆断电续费超时时间（分钟）
    const val DEFAULT_GOOGLE_NET_TIMEOUT = 3 //请求谷歌网络默认超时时间（秒）
    const val SUC = "SUCESS" //
    const val FAIL = "FAIL" //

    object SpKey {
        const val TOKEN = "SP_TOKEN"
        const val UCASTA = "SP_UCASTA"
        const val USER = "SP_USER"
        const val USERBALANCE = "USERBALANCE"
        const val SP_PERSONINFO = "SP_PERSONINFO"//是否完善个人信息
        const val USERDATA = "SP_USERDATA"
        const val LOGIN_COOKIE = "SP_LOGIN_COOKIE"
        const val SPLASH_AD = "SP_SPLASH_AD"
        const val AD_DEPOSIT = "SP_AD_DEPOSIT"
        const val PHONE_COUNTRY = "SP_PHONE_COUNTRY"
        const val DEVICE_ID = "DEVICE_ID"
        const val STRIPE_PUB_KEY = "STRIPE_PUB_KEY"
        const val GOOGLE_NET_TIMEOUT = "GOOGLE_NET_TIMEOUT"
        const val PAYPAL_PAY_CONFIG = "PAYPAL_PAY_CONFIG"
        const val SP_COUNTRY_SEL = "SP_COUNTRY_SEL"//是否选择国家码
        const val SP_AUTH = "SP_AUTH"//是否授予
        const val SP_beginner = "SP_beginner"//新手指引
        const val SP_NEWUSERCAR= "SP_NEWUSERCAR"//第一次临停
        const val SP_LANG = "SP_LANG"// 多语言选择
        const val SP_ADTIME = "SP_ADTIME"//首页广告弹框时间
        const val SP_NOTIFYTIME = "SP_NOTIFYTIME"//通知权限时间
        const val SP_APPCODE = "SP_APPCODE"//app 版本一样 更新提示时间
        const val SP_ADTIME_CONT = "SP_ADTIME_CONT"//首页广告弹框内容
        const val SP_VERSION = "SP_VERSION"//服务器版本号
        const val SP_SPLAD = "SP_SPLAD"//开屏广告
        const val SP_SPLAD_THIRD = "SP_SPLAD_THIRD"//第三方开屏广告
        const val SP_VECHNO = "SP_VECHNO"//当前骑行中的车辆编号
        const val SP_FIXPWD = "SP_FIXPWD"//是否修改密码
        const val SP_LOGINTYPE = "SP_LOGINTYPE"//登录类型
        const val SP_BLE_PLE = "SP_BLE_PLE"//是否有蓝牙权限
        const val SP_BLE_PLE_DEFY = "SP_BLE_PLE_DEFY"//是否拒绝蓝牙权限
        const val SP_NEWUSER = "SP_NEWUSER"// 是否新用户注册-赠送金额
        const val SP_LOG = "SP_LOG"// 日志保存
        const val SP_COUNTRY_LOCAL = "SP_COUNTRY_LOCAL"// 选择国家本地缓存
        const val SP_COUNTRY = "SP_COUNTRY"// 选择国家
        const val SP_COUNTRY_sel = "SP_COUNTRY_sel"// 国家列表 选择国家
        const val SP_COUNTRY_UNIT = "SP_COUNTRY_UNIT"// 选择国家货币单位

        const val SP_REFUND_DEPOSIT = "SP_REFUND_DEPOSIT"// 退款开户行
        const val SP_REFUND_NAME = "SP_REFUND_NAME"// 退款名字
        const val SP_REFUND_ACCOUNT = "SP_REFUND_ACCOUNT"// 退款账号
        const val SP_TESTNET = "SP_TESTNET"// 测试环境
        const val SP_RANTAL_DIG = "SP_RANTAL_DIG"// 长租弹框
        const val SP_UUID = "SP_UUID"// 用户uuid
        const val SP_LOGIN_UUID = "SP_LOGIN_UUID"// 用户uuid是否登录 神策
        const val SP_ADTIME_SPAL = "SP_ADTIME_SPAL"//开屏广告时间
        const val SP_PERM_CAM = "SP_PERM_CAM"// 相机权限
        const val SP_PERM_NOTIFY = "SP_PERM_NOTIFY"// 通知权限
    }

    object Event {
        const val LOGIN = "LOGIN"
        const val ONE_PASS_LOGIN = "ONE_PASS_LOGIN"
        const val LOGOUT = "LOGOUT"
        const val USER_UPDATE = "USER_UPDATE"
        const val LOCATION_UPDATE = "LOCATION_UPDATE"
        const val GET_AD_DEPOSIT = "GET_AD_DEPOSIT"
        const val CHECK_GOOGLE_NET_RESULT = "CHECK_GOOGLE_NET_RESULT"
        const val BLE_CLOSECARSUC = "BLE_CLOSECARSUC"
        const val BLE_CLOSECARFAIL = "BLE_CLOSECARFAIL"
        const val BLE_CONFAIL = "BLE_CONFAIL"
        const val BLE_CONFAIL_AUTO = "BLE_CONFAIL_AUTO"
        const val BLE_CONSUC = "BLE_CONSUC"
        const val REFUNDSUC = "REFUNDSUC"
        const val NOTIFY = "NOTIFY"
        const val NOTIFYPOWER = "NOTIFYPOWER"
        const val EVENT_PICRETURN = "EVENT_PICRETURN"  //长租拍照还车事件
        const val EVENT_ORDERFISH = "EVENT_ORDERFISH"  //订单结束事件
        const val EVENT_ORDERFISHERR = "EVENT_ORDERFISHERR"  //订单结束事件错误
        const val EVENT_COST_DEL = "EVENT_COST_DEL"  //故障上报成功后 通知删除对应订单
//        const val EVENT_LEASESUC = "EVENT_LEASESUC"  //创建租赁订单成功
        const val EVENT_ADSPAL_SUC = "EVENT_ADSPAL_SUC"  //开屏广告成功
        const val EVENT_ADSPAL_FAIL = "EVENT_ADSPAL_FAIL"  //开屏广告失败
    }

    object ErrCode {
        const val SUCCESS = 1
        const val FAILED = -1
        const val TIMEOUT = -2
        const val CANCEL = -3
        const val ROUTE_TO_NEAREST_PARK_POINT = -4
        const val ONLY_DISMISS = -5
        const val CHECK_PAY_CARD_BIND = -6
        const val JUMP_LINE_PAY = -7
        const val JUMP_LIANLIAN_PAY = -8
        const val JUMP_TO_CHARGE = -9
        const val BLE_OFFSET = Int.MIN_VALUE / 2
        const val UN_LOGIN = -1002
        const val STRIPE_CHARGE_TOO_SMALL = -190001 //支付金额小于stripe支付要求的最低金额
        const val PAYPAY_NOT_AUTH = -25001 //paypay支付未获取到用户授权信息

        const val RIDE_LOW = 200003 //最低骑行额度
        const val SCAN_MUCH = 300002 //频繁扫码
        const val USIGNOUT = 200408 //无法注销
        const val RIDE_ORDER = 300601 //创建骑行订单错误
        const val LOGIN_GOOGLE = 200410 //第三方登录 第一次
        const val LOGIN_GOOGLE_NEW = 200404 //第三方登录 新用户
        const val LOGIN_BLACK = 200407 //黑名单
        const val LOGIN_CHECK = 200405 //用户注销 审核中
        const val ORDER_PAYMENT = 300202 //结算骑行订单
        const val ORDER_PAYCOMPANY = 300203 //订单已支付
        const val no_permission = 100400 //无权限
        const val no_area = 300400 //无运营区
        const val noPermiss = 200403 //用户凭证过期
        const val uPermiss = 200400 //用户凭证无效
        const val NET_CLOSECAR_FAIL = 300114 //结束订单 关锁失败
        const val NET_CLOSECAR_FAIL_type1 = 1 //还车指令下发失败
        const val NET_CLOSECAR_FAIL_type2 = 2 //中控未响应还车指令
        const val NET_CLOSECAR_FAIL_type3 = 3 //中控返回还车失败
    }

    object MapType {
        const val ORIGIN = 0
        const val BAIDU = 1
        const val GAODE = 2 //高德或谷歌
    }

    object GeoType {
        const val CIRCLE = 0
        const val POLYGON = 1
    }

    object PointType {
        const val CIRCLE = 0
        const val POLYGON = 1
    }

    object DepositState {
        const val NOT_YET = 0
        const val ALREADY = 1
        const val RETURNING = 2
        const val ZHIMA_FREE = 3
        const val LOCK = 4
        const val STUDENT_FREE = 5
        const val MONEY_LESS = 6
    }

    object AdvertisingType {
        const val SPLASH = 0 //	启动页广告
        const val MAIN_DIALOG = 1 //	首页弹窗广告
        const val MAIN_TOP = 2 //	首页顶部广告
    }

    object CouponType {
        const val MONEY = 0
        const val TIME = 1
    }

    object TransactionType {
        const val CONSUMPTION = 0
        const val RECHARGE = 1
    }

    object DispatchType {
        const val IN_STATION = 1
        const val UNSUPPORTED = 2
        const val OUT_AREA = 3
        const val OUT_STATION = 4
        const val HELMET_NOT_LOCK = 5 //头盔未锁
        const val RFID = 10
    }

    object MemberFeeType {
        const val DAY = 0 //天卡
        const val WEEK = 1 //周卡
        const val MONTH = 2 //月卡
        const val SEASON = 3 //季卡
        const val HALF_YEAR = 4 //半年卡
        const val YEAR = 5 //年卡
    }

    object CardType {
        const val DAY = 0 //天卡
        const val WEEK = 1 //周卡
        const val MONTH = 2 //月卡
        const val SEASON = 3 //季卡
        const val HALF_YEAR = 4 //半年卡
        const val YEAR = 5 //年卡
        const val DURATION = 6 //时长卡(骑行套餐)
    }

    object ModelType {
        const val PARK_POINT = 0
        const val PROHIBIT_AREA = 1
    }

    object ValidType {
        const val INVALID = 0
        const val VALID = 1
        const val NOT_ACTIVE = 2
    }

    object ORDER_SOURCE {
        var ANDROID = "1"
        const val IOS = 2
        const val MINI_PROGRAM = 3
    }

    object MessageState {
        const val UNREAD = 0
        const val HAVE_READ = 1
    }

    object EventType {
        const val BORROW_BIKE = 0
        const val RETURN_BIKE = 1
    }

    object PayWay {
        const val ALIPAY = 0
        const val WXPAY = 1
        const val PAYPAL = 2
        const val STRIPE = 3
        const val PAYPAY = 4
        const val LINE = 5
        const val BALANCE = 6
        const val LIANLIAN = 7
    }

    //获取支付方式接口返回的类型
    object NetPayMethods {
        const val BALANCE = 0
        const val WXPAY = 1
        const val ALIPAY = 3
        const val PAYPAL = 6
        const val LINE = 10
        const val STRIPE = 11
        const val PAYPAY = 12
        const val LETIAN = 13
        const val LIANLIAN = 14
    }

    object BusType {
        const val CHARGE = 0
        const val MEMBER_FEE = 1
        const val RIDE_CARD = 2
        const val VIP_CARD = 3
    }

    object DIR {
        val UPDATE = File(ContextUtil.getContext().getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "update")
        val IMAGE = File(ContextUtil.getContext().filesDir, "image").absolutePath
    }

    /**
     * 非文明骑行类型
     */
    object UncivilizedRidingType {
        const val RUN_RED = 1 //闯红灯
        const val RETROGRADE = 2 //逆行
        const val TAKE_MOTORWAY = 3 //走机动车道
        const val MULTI_RIDING = 4 //多人骑行
    }

    /**
     * 非文明骑行申诉状态
     */
    object UncivilizedRidingAppealState {
        const val NOT_APPEAL = 0 //未申诉
        const val APPEALING = 1 //申诉中
        const val APPEALED = 2 //已申诉（已处理）
    }

    /**
     * 非文明骑行申诉处理结果
     */
    object UncivilizedRidingAppealResult {
        const val UNHANDLED = 0 //未处理
        const val SUCCESS = 1 //申述通过
        const val FAILED = 2 //申述失败
    }

    //远程控制定义
    object CtrlType {
        const val UNLOCK = 1    //开锁
        const val LOCK = 2    //上锁
        const val UNLOCK_BATTERY = 3    //开电池锁
        const val LOCK_BATTERY = 4    //上电池锁
        const val FIND_BIKE = 6    //寻车
        const val REQUEST_LOCATION = 5    //立即定位
        const val ALARM = 7    //警报
        const val UNLOCK_HELMET = 8    //开头盔锁
        const val LOCK_HELMET = 9    //关头盔锁
        const val REQUEST_LOCATION_VOICE = 14    //定位声音
        const val REQUEST_BATERY_OPEN_VOICE = 15    //开电池锁声音
        const val REQUEST_BATERY_CLOSE_VOICE = 16    //关电池锁声音
    }

    object CommandState {
        const val STATE_INIT = 0
        const val STATE_SEND_SUCCESS = 1
        const val STATE_EXECUTE_SUCCESS = 2
    }

    object CommandExecuteType {
        const val ONLY_BLE = 0
        const val ONLY_NET = 1
        const val BLE_AND_NET = 2
        const val NET_AND_BLE = 3
    }

    object CtrlParamName {
        const val SEND_LOCATION = "8"
        const val FIND_BIKE = "9"
        const val UNLOCK_HELMET = "22"
        const val LOCK_HELMET = "23"
    }

    object FunctionSupportType {
        const val YES = 1
        const val NO = 0
    }

    object SupportedFunction {
        const val HELMET = 1
        const val RFID = 6
    }

    object ProtocolPassMethod {
        const val TICK_AGREE = 1//勾选即同意
        const val FORCE_READ = 2//强制阅读
        const val FORCE_SIGN = 3//强制签字
    }

    object InviteMode {
        const val CODE = 1 //邀请码
        const val PHONE = 2 //手机号
    }

    object CustomerServiceType {
        const val PHONE = 1
        const val EMAIL = 2
        const val FACEBOOK = 3
        const val LINE = 4
    }

    object BatLevel {
        const val Bat_H = 60
        const val Bat_M = 20
    }

    object OrderType {
        const val RENTAL_Y = 4 //长租类型
        const val RENTAL_N = 1 //短租类型
    }

}