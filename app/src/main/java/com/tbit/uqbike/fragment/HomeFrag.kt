package com.tbit.uqbike.fragment

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.AuthActivity
import com.tbit.uqbike.activity.HomeActivity
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.activity.RecordDetailActivity
import com.tbit.uqbike.activity.ScanForBorrowActivity
import com.tbit.uqbike.activity.UserCarActivity
import com.tbit.uqbike.activity.WalletActivity
import com.tbit.uqbike.activity.WebActivity
import com.tbit.uqbike.activity.WebActionActivity
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.base.BaseFragment
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.InviteDialog
import com.tbit.uqbike.dialog.UserHelpBottomSheep
import com.tbit.uqbike.dialog.kfBottomSheep
import com.tbit.uqbike.dialog.util.MainDialogUtil
import com.tbit.uqbike.dialog.util.RidingDialogUtil
import com.tbit.uqbike.entity.CarInfoData
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.MyOrderStateData
import com.tbit.uqbike.entity.ParkData
import com.tbit.uqbike.entity.RIG_TYPE_NEWUSER
import com.tbit.uqbike.entity.RigisterData
import com.tbit.uqbike.entity.SysMinAmount
import com.tbit.uqbike.entity.VehicleData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.getAdData
import com.tbit.uqbike.entity.getAreaData
import com.tbit.uqbike.entity.isFirstOrderData
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.entity.type_coupon_invite
import com.tbit.uqbike.entity.type_coupon_money
import com.tbit.uqbike.entity.type_coupon_ridecard
import com.tbit.uqbike.mvp.constract.MainContract
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.mvp.presenter.MainPresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.tbit.uqbike.web.js.util.webUtil
import com.tbit.uqbike.widget.CustomNestedScrollView
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.subjects.PublishSubject
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.support.v4.startActivity
import org.json.JSONObject
import java.util.concurrent.TimeUnit
import wendu.dsbridge.DWebView

/**
 * 首页Fragment
 * 主要功能：地图展示和交互、用户功能入口、广告管理、骑行状态管理
 */
class HomeFrag : BaseFragment(), MainContract.View {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.frag_home, container, false)
    }

    // Fragment引用
    private val mainMapFragment by lazy { childFragmentManager.findFragmentById(R.id.main_map_fragment) as MainMapFragment }
    private val mainBusinessFragment by lazy { childFragmentManager.findFragmentById(R.id.main_upda_fragment) as MainBusinessFragment }
    val mainAdFragment by lazy { childFragmentManager.findFragmentById(R.id.main_ad_fragment) as HomeAdFragment }
    private val mainAdFullFragment by lazy { childFragmentManager.findFragmentById(R.id.main_adfull_fragment) as? MainAdFullFragment }
    private var MyView : View? = null
    private var pendingAreaId: Long? = null

    // H5推荐区域动态高度标记
    private var h5RecommendHeightAdjusted = false

    // 地图委托对象
    private val geoMapDelegate by lazy { mainMapFragment.geoMapDelegate }
    private val bikeMapDelegate by lazy { mainMapFragment.bikeMapDelegate }
    private val parkPointMapDelegate by lazy { mainMapFragment.parkPointMapDelegate }
    private val prohibitAreaMapDelegate by lazy { mainMapFragment.prohibitAreaMapDelegate }
    private val routeLineMapDelegate by lazy { mainMapFragment.routeLineMapDelegate }
    private val presenter = MainPresenter(this)

    // 数据加载控制
    private val publish = PublishSubject.create<Int>()
    private val loadDataDisposable = publish.sample(1, TimeUnit.SECONDS)
        .observeOn(AndroidSchedulers.mainThread())
        .subscribeBy(onNext = { loadDataImpl() })
    
    @RequiresApi(Build.VERSION_CODES.M)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        this.MyView = view
        super.onViewCreated(view, savedInstanceState)
        
        EventBus.getDefault().register(this);
        lifecycle.addObserver(presenter)

        // 触摸事件处理 - 禁用ViewPager滑动和ScrollView滚动
        val touchListener = View.OnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                (activity as HomeActivity).setViewPageMove(false)
                MyView?.findViewById<CustomNestedScrollView>(R.id.nscl)?.setEnableScroll(false)
            }
            false
        }
        mainAdFragment.getMyView()?.findViewById<RelativeLayout>(R.id.rl_ad_banner_over)?.setOnTouchListener(touchListener)
        MyView?.findViewById<RelativeLayout>(R.id.rl_map)?.setOnTouchListener(touchListener)

        // 地图Fragment回调设置
        mainMapFragment.onAreaIdSucListener = {
            getbottomAdData()
            mainAdFragment.getData()
            onAreaIdSucListener("HomeFrag_MapListener")
            if (!Glob.isLogin){
                if (Glob.isRental){
                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.scan_lease)
                }else{
                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(com.tbit.uqbike.R.string.scan_unlock)
                }
            }else{
                getRideState()
                mainBusinessFragment.getInviteData()
            }
            (activity as HomeActivity).getMapData()
        }
        
        mainMapFragment.onAreaIdFailListener = {
            getbottomAdData()
            mainAdFragment.getData()
            onAreaIdFailListener("HomeFrag_MapListener")
            (activity as HomeActivity).getMapData()
        }
        
        mainMapFragment.onSearchCenterChangeListener = { onSearchCenterChange() }
        mainMapFragment.onMsgUresdListener = {}
        
        // 设置顶部广告状态
        mainAdFragment.setTopState()

        mainMapFragment.sumitLocal(false)

        // 底部菜单按钮点击事件
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_balance)?.clickDelay {
            MDUtil.clickEvent("wallet_icon_click", JSONObject())
            if(!(activity as BaseActivity).hasLocationPermission()){
                (activity as BaseActivity).openLocal()
                return@clickDelay
            }
            if ((activity as BaseActivity).tryLoginIfNot()) activity?.startActivity<WalletActivity>()
        }
        
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_cost)?.clickDelay {
            MDUtil.clickEvent("billing_rules_icon_click", JSONObject())
            if ((activity as BaseActivity).tryLoginIfNot()) {
                loadingDialogHelper!!.show {  }
                PageModel.getPageUrl(PageModel.billing_rules).subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                        activity?.startActivity<WebActivity>(
                            WebActivity.TITLE to getString(R.string.billing_rules),
                            WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                    },
                    onError = {loadingDialogHelper!!.dismiss()}
                ).toCancelable()
            }
        }
        
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_more)?.clickDelay {
            MDUtil.clickEvent("more_icon_click", JSONObject())
            if(!IsAuth()) return@clickDelay
            if(!(activity as BaseActivity).hasLocationPermission()){
                (activity as BaseActivity).openLocal()
                return@clickDelay
            }
            if ((activity as BaseActivity).tryLoginIfNot()) {
                lifecycleDialogHelper.show(UserHelpBottomSheep((activity as BaseActivity)))
            }
        }
        
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_hotline)?.clickDelay {
            MDUtil.clickEvent("contact_icon_click", JSONObject())
            if(!IsAuth()) return@clickDelay
            if(!(activity as BaseActivity).hasLocationPermission()){
                (activity as BaseActivity).openLocal()
                return@clickDelay
            }
            if ((activity as BaseActivity).tryLoginIfNot()) {
                lifecycleDialogHelper.show(kfBottomSheep((activity as BaseActivity)))
            }
        }

        // 扫码按钮点击事件
        MyView?.findViewById<RelativeLayout>(R.id.rl_scanner)?.clickDelay {
            val eventName = if (Glob.isRental) "scan_rent_car_click" else "scan_ride_bike_click"
            MDUtil.clickEvent(eventName, JSONObject())
            
            if(!IsAuth()) return@clickDelay
            
            if ((activity as BaseActivity).tryLoginIfNot()) {
                // 相机权限检查
                if (!(activity as BaseActivity).hasCamPermission()){
                    if (SpUtil.getInstance().find(Constant.SpKey.SP_PERM_CAM).isNullOrEmpty()){
                        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_PERM_CAM,"1")
                    }else{
                        CommDialog.Builder(activity).setTitle(getString(R.string.s_per_carm_title)).setContent(getString(R.string.s_per_carm_cont))
                            .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(R.string.s_set)).setCanceledOnOutside(true)
                            .setClickListen(object : CommDialog.TwoSelDialog {
                                override fun leftClick() {}
                                override fun rightClick() {
                                    LocationUtil.gotoLocalPermiss(activity!!)
                                }
                            }).build().show()
                        return@clickDelay
                    }
                }

                (childFragmentManager.findFragmentById(R.id.fragment_camera_permission) as CameraPermissionFragment).requestPermission {
                    if(!(activity as BaseActivity).hasLocationPermission()){
                        (activity as BaseActivity).openLocal()
                    }else{
                        if (!LocationUtil.isGpsEnabled()) {
                            MainDialogUtil.GpsDig(activity as HomeActivity)
                            return@requestPermission
                        }
                        
                        if(MainState == 2){
                            val isRental = MainOrderState == Constant.OrderType.RENTAL_Y
                            activity?.startActivity(RecordDetailActivity.createIntent(activity!!,orderNo,false,isRental))
                        }else if(MainState == 1){
                            startActivity<MainActivity>()
                        }else{
                            UserModel.getCreatRideOrder()
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper?.dismiss()
                                        MyLogUtil.Log("1111","用户是否可以创建订单==="+it.toString())
                                        
                                        if (!it.toString().equals("[]")){
                                            val resultData = Gson().fromJson(it.toString(), isFirstOrderData::class.java)
                                            if (resultData != null) {
                                                isFirstOrder = resultData.is_first
                                                if (resultData.is_first_temp){
                                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"1")
                                                }else{
                                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"")
                                                }
                                            }
                                        }else{
                                            isFirstOrder = false
                                        }
                                        
                                        if (Glob.isRental){
                                            goToScanBorrow()
                                        }else{
                                            RidingDialogUtil.showRideCardDialog(activity as BaseActivity, onNotify = {goToScanBorrow()})
                                        }
                                    },
                                    onError = {
                                        val errCode = ErrHandler.getErrCode(it)
                                        if (errCode == Constant.ErrCode.RIDE_LOW){
                                            val listKey = arrayOf("sys_minimum_amount")
                                            ComModel.getConfig(listKey).subscribeBy(
                                                onNext = {
                                                    loadingDialogHelper?.dismiss()
                                                    MyLogUtil.Log("1111","===获取最低骑行额度 信息=="+it.toString())
                                                    val resultData = Gson().fromJson(it.toString(), SysMinAmount::class.java)
                                                    MainDialogUtil.MinAmountDig(activity as BaseActivity,resultData.sys_minimum_amount)
                                                },
                                                onError = {loadingDialogHelper?.dismiss()}
                                            ).toCancelable()
                                        }else{
                                            MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                            loadingDialogHelper?.dismiss()
                                        }
                                    }
                                ).toCancelable()
                        }
                    }
                }
            }
        }

        // 其他按钮点击事件
        MyView?.findViewById<ImageView>(R.id.image_big)?.setOnClickListener {
            if (!Glob.isLogin) {
                if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                    MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                    return@setOnClickListener
                }

                MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                FlavorConfig.appRoute.login()
                return@setOnClickListener
            }
            startActivity<MainActivity>()
        }
        
        MyView?.findViewById<RoundTextView>(R.id.rv_seeOrder)?.setOnClickListener {
            if (!Glob.isLogin) {
                if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                    MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                    return@setOnClickListener
                }

                MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                FlavorConfig.appRoute.login()
                return@setOnClickListener
            }
            startActivity<MainActivity>()
        }

        checkGoshopH5Enable(view)

        // 添加调试按钮（临时用于测试）
        view.findViewById<View>(R.id.rv_seeOrder)?.setOnLongClickListener {
            MyLogUtil.Log("1111", "H5推荐区: 手动触发重新加载")
            reloadH5RecommendArea()
            true
        }
    }



    // H5推荐区域功能
    private fun checkGoshopH5Enable(rootView: View) {
        // 检查网络状态
        if (!NetUtils.isConnected(rootView.context)) {
            MyLogUtil.Log("1111", "H5推荐区: 网络未连接，隐藏H5区域")
            rootView.findViewById<ViewGroup>(R.id.layout_h5_area).visibility = View.GONE
            return
        }

        val apiUrl = FlavorConfig.NET.COM_URL + "1.0/common/config/self/taokeisopen"
        MyLogUtil.Log("1111", "H5推荐区: ========== 开始H5推荐区域初始化 ==========")
        MyLogUtil.Log("1111", "H5推荐区: 网络状态: 已连接")
        MyLogUtil.Log("1111", "H5推荐区: API地址: $apiUrl")
        MyLogUtil.Log("1111", "H5推荐区: GOSHOP_WEB_HOST: ${FlavorConfig.NET.GOSHOP_WEB_HOST}")
        MyLogUtil.Log("1111", "H5推荐区: 当前语言: ${FlavorConfig.Local.language}")

        ComModel.getTaoKeIsOpen().subscribeBy(
            onNext = {
                val json = it.toString()
                MyLogUtil.Log("1111", "H5推荐区: 'taokeisopen' 接口返回: $json")

                val isOpen = try {
                    val value = org.json.JSONObject(json).opt("is_open")
                    val result = when (value) {
                        is Number -> value.toDouble() == 1.0
                        is String -> value == "1"
                        else -> false
                    }
                    MyLogUtil.Log("1111", "H5推荐区: 解析结果 is_open=$value, isOpen=$result")
                    result
                } catch (e: Exception) {
                    MyLogUtil.Log("1111", "H5推荐区: 解析 'taokeisopen' 响应异常: ${e.message}")
                    false
                }

                val h5Area = rootView.findViewById<ViewGroup>(R.id.layout_h5_area)
                if (isOpen) {
                    MyLogUtil.Log("1111", "H5推荐区: 配置为开启, 显示H5区域.")
                    h5Area.visibility = View.VISIBLE

                    if (h5Area.childCount == 0) {
                        MyLogUtil.Log("1111", "H5推荐区: H5区域为空, 正在创建并加载WebView.")

                        val webView = DWebView(rootView.context)
                        val jsApi = com.tbit.uqbike.web.js.JsApi()
                        webView.addJavascriptObject(jsApi, null)

                        // 设置JavaScript API监听器，处理H5推荐区域的交互
                        jsApi.setOnItemClickListener(object : com.tbit.uqbike.web.js.JsApi.OnClickListener {
                            override fun onClickItem(msg: String?, handler: wendu.dsbridge.CompletionHandler<String>) {
                                MyLogUtil.Log("1111", "H5推荐区: JavaScript调用 =====$msg")
                                handleH5RecommendAction(msg, handler)
                            }
                        })

                        // 检查GOSHOP_WEB_HOST是否配置
                        val goshopHost = try {
                            FlavorConfig.NET.GOSHOP_WEB_HOST
                        } catch (e: Exception) {
                            MyLogUtil.Log("1111", "H5推荐区: GOSHOP_WEB_HOST未配置，使用默认值")
                            "https://www.gogo-shop.com"
                        }

                        if (!goshopHost.isNullOrBlank()) {
                            val url = "$goshopHost/app/wealth/tao_recommend?locale=" + FlavorConfig.Local.language
                            MyLogUtil.Log("1111", "H5推荐区: WebView加载URL: $url")

                            // 验证URL有效性
                            if (!url.isBlank() && url.startsWith("http")) {
                                // 完善的WebView配置
                                val settings = webView.settings
                                settings.javaScriptEnabled = true
                                settings.domStorageEnabled = true
                                settings.allowUniversalAccessFromFileURLs = true
                                settings.allowFileAccessFromFileURLs = true
                                settings.allowContentAccess = true
                                settings.allowFileAccess = true
                                settings.databaseEnabled = true
                                settings.cacheMode = android.webkit.WebSettings.LOAD_DEFAULT
                                settings.offscreenPreRaster = true

                                // 支持缩放和用户代理
                                settings.setSupportZoom(false)
                                settings.builtInZoomControls = false
                                settings.displayZoomControls = false
                                settings.useWideViewPort = true
                                settings.loadWithOverviewMode = false  // 改为false，避免缩放导致viewport问题

                                // 设置初始缩放比例为100%，确保内容按实际尺寸显示
                                webView.setInitialScale(100)

                                // 设置用户代理
                                val userAgent = settings.userAgentString
                                settings.userAgentString = "$userAgent GoGoApp/${com.tbit.uqbike.BuildConfig.VERSION_NAME}"

                                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
                                    settings.mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                                }

                                webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                                // 允许WebView内部滚动，确保内容能完整显示
                                webView.overScrollMode = View.OVER_SCROLL_IF_CONTENT_SCROLLS
                                webView.isVerticalScrollBarEnabled = false  // 隐藏滚动条但允许滚动
                                webView.isHorizontalScrollBarEnabled = false

                                // 设置WebViewClient处理页面加载
                                webView.webViewClient = object : android.webkit.WebViewClient() {
                                    override fun onPageStarted(view: android.webkit.WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                                        super.onPageStarted(view, url, favicon)
                                        MyLogUtil.Log("1111", "H5推荐区: 开始加载页面: $url")
                                    }

                                    override fun onPageFinished(view: android.webkit.WebView?, url: String?) {
                                        super.onPageFinished(view, url)
                                        MyLogUtil.Log("1111", "H5推荐区: ✅ 页面加载完成: $url")

                                        // 检查页面内容
                                        view?.evaluateJavascript("document.body.innerHTML.length") { result ->
                                            MyLogUtil.Log("1111", "H5推荐区: 页面内容长度: $result")
                                        }

                                        view?.evaluateJavascript("document.title") { result ->
                                            MyLogUtil.Log("1111", "H5推荐区: 页面标题: $result")
                                        }
                                    }

                                    override fun onReceivedError(view: android.webkit.WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                                        super.onReceivedError(view, errorCode, description, failingUrl)
                                        MyLogUtil.Log("1111", "H5推荐区: 页面加载错误 - 错误码:$errorCode, 描述:$description, URL:$failingUrl")
                                    }

                                    override fun onReceivedHttpError(view: android.webkit.WebView?, request: android.webkit.WebResourceRequest?, errorResponse: android.webkit.WebResourceResponse?) {
                                        super.onReceivedHttpError(view, request, errorResponse)
                                        MyLogUtil.Log("1111", "H5推荐区: HTTP错误 - 状态码:${errorResponse?.statusCode}, URL:${request?.url}")
                                    }

                                    override fun shouldOverrideUrlLoading(view: android.webkit.WebView?, request: android.webkit.WebResourceRequest?): Boolean {
                                        val requestUrl = request?.url?.toString()
                                        MyLogUtil.Log("1111", "H5推荐区: 拦截URL跳转: $requestUrl")
                                        return false // 让WebView处理URL
                                    }
                                }

                                // 计算智能固定高度，防止布局跳跃
                                val fixedHeight = calculateH5RecommendHeight(rootView.context)
                                MyLogUtil.Log("1111", "H5推荐区: 设置智能计算的固定高度 ${fixedHeight}px")

                                // 添加请求头并加载URL
                                val headers = mutableMapOf<String, String>()
                                headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
                                headers["Accept-Language"] = FlavorConfig.Local.language
                                headers["Cache-Control"] = "no-cache"

                                MyLogUtil.Log("1111", "H5推荐区: 开始加载URL: $url")
                                MyLogUtil.Log("1111", "H5推荐区: 请求头: $headers")

                                // 使用智能计算的固定高度LayoutParams，防止WebView高度变化导致布局跳跃
                                val layoutParams = FrameLayout.LayoutParams(
                                    FrameLayout.LayoutParams.MATCH_PARENT,
                                    fixedHeight
                                )
                                h5Area.addView(webView, layoutParams)

                                // 标记这个WebView为H5推荐区域，用于后续识别
                                webView.tag = "h5_recommend_webview"

                                // 确保H5区域可见
                                h5Area.visibility = View.VISIBLE
                                MyLogUtil.Log("1111", "H5推荐区: WebView已添加到布局，高度=${fixedHeight}px，可见性=${h5Area.visibility}")

                                // 加载URL
                                webView.loadUrl(url, headers)
                            } else {
                                MyLogUtil.Log("1111", "H5推荐区: URL无效: $url")
                                h5Area.visibility = View.GONE
                            }
                        } else {
                            MyLogUtil.Log("1111", "H5推荐区: GOSHOP_WEB_HOST为空，隐藏H5区域")
                            h5Area.visibility = View.GONE
                        }
                    } else {
                        MyLogUtil.Log("1111", "H5推荐区: H5区域已存在WebView, 不重复创建.")
                        // 确保现有WebView也有正确的标记和智能计算的固定高度
                        val existingWebView = h5Area.getChildAt(0) as? DWebView
                        if (existingWebView != null) {
                            existingWebView.tag = "h5_recommend_webview"
                            val fixedHeight = calculateH5RecommendHeight(rootView.context)
                            val layoutParams = existingWebView.layoutParams
                            if (layoutParams.height != fixedHeight) {
                                layoutParams.height = fixedHeight
                                existingWebView.layoutParams = layoutParams
                                MyLogUtil.Log("1111", "H5推荐区: 更新现有WebView高度为智能计算值 ${fixedHeight}px")
                            }
                        }
                    }
                } else {
                    MyLogUtil.Log("1111", "H5推荐区: 配置为关闭, 隐藏H5区域.")
                    h5Area.visibility = View.GONE
                }
            },
            onError = {
                MyLogUtil.Log("1111", "H5推荐区: 'taokeisopen' API请求异常: ${it.message}")
                MyLogUtil.Log("1111", "H5推荐区: API请求异常类型: ${it::class.java.name}")
                MyLogUtil.Log("1111", "H5推荐区: API请求异常堆栈: ${android.util.Log.getStackTraceString(it)}")
                it.printStackTrace()
                rootView.findViewById<ViewGroup>(R.id.layout_h5_area).visibility = View.GONE
            }
        )
    }

    // 处理H5推荐区域的JavaScript交互
    private fun handleH5RecommendAction(msg: String?, handler: wendu.dsbridge.CompletionHandler<String>) {
        try {
            val resultData = Gson().fromJson(msg, com.tbit.uqbike.entity.JsData::class.java)
            if (resultData != null) {
                MyLogUtil.Log("1111", "H5推荐区: 处理JavaScript调用类型: ${resultData.type}")

                when (resultData.type) {
                    webUtil.Type_setWebpageHeight -> {
                        // H5推荐区域根据内容高度进行智能调整
                        try {
                            // 直接从JSON中解析高度值
                            val jsonObject = org.json.JSONObject(msg)
                            val dataObject = jsonObject.getJSONObject("data")
                            val requestedHeight = dataObject.getDouble("height").toInt()

                            MyLogUtil.Log("1111", "H5推荐区: 页面请求高度调整为 ${requestedHeight}px")

                            // 获取当前H5区域
                            val h5Area = MyView?.findViewById<FrameLayout>(R.id.layout_h5_area)
                            val webView = h5Area?.getChildAt(0) as? DWebView

                            if (h5Area != null && webView != null) {
                                // 计算合适的高度：在请求高度和最大允许高度之间选择
                                val screenHeight = requireContext().resources.displayMetrics.heightPixels
                                val maxAllowedHeight = (screenHeight * 0.6f).toInt()
                                val minHeight = (300 * requireContext().resources.displayMetrics.density).toInt()

                                val finalHeight = requestedHeight.coerceIn(minHeight, maxAllowedHeight)

                                MyLogUtil.Log("1111", "H5推荐区: 调整高度 - 请求:${requestedHeight}px, 最大允许:${maxAllowedHeight}px, 最终:${finalHeight}px")

                                // 在主线程中更新WebView高度
                                activity?.runOnUiThread {
                                    try {
                                        val layoutParams = webView.layoutParams
                                        layoutParams.height = finalHeight
                                        webView.layoutParams = layoutParams
                                        h5RecommendHeightAdjusted = true  // 标记高度已被动态调整

                                        // 调试：检查实际的布局参数
                                        MyLogUtil.Log("1111", "H5推荐区: 高度调整完成，新高度=${finalHeight}px，已标记为动态调整")
                                        MyLogUtil.Log("1111", "H5推荐区: WebView实际高度=${webView.height}px，布局参数高度=${layoutParams.height}px")
                                        MyLogUtil.Log("1111", "H5推荐区: H5Area高度=${h5Area.height}px，可见性=${h5Area.visibility}")

                                        // 强制重新测量和布局
                                        h5Area.requestLayout()
                                        webView.requestLayout()

                                        // 强制父容器也重新布局
                                        val parentView = h5Area.parent as? android.view.View
                                        parentView?.requestLayout()

                                        // 延迟检查实际效果
                                        h5Area.post {
                                            MyLogUtil.Log("1111", "H5推荐区: 布局后 - WebView实际高度=${webView.height}px，H5Area高度=${h5Area.height}px")

                                            // 如果WebView高度仍然不正确，强制设置
                                            if (webView.height != finalHeight) {
                                                MyLogUtil.Log("1111", "H5推荐区: WebView高度仍不正确，强制重新设置")
                                                val newLayoutParams = FrameLayout.LayoutParams(
                                                    FrameLayout.LayoutParams.MATCH_PARENT,
                                                    finalHeight
                                                )
                                                webView.layoutParams = newLayoutParams
                                                h5Area.requestLayout()

                                                // 再次延迟检查
                                                h5Area.postDelayed({
                                                    MyLogUtil.Log("1111", "H5推荐区: 强制设置后 - WebView实际高度=${webView.height}px，H5Area高度=${h5Area.height}px")
                                                }, 100)
                                            }

                                            // 强制设置WebView的viewport高度与容器高度匹配
                                            webView.evaluateJavascript("""
                                                (function() {
                                                    // 设置viewport高度为容器高度
                                                    var containerHeight = ${finalHeight};
                                                    var density = ${requireContext().resources.displayMetrics.density};
                                                    var viewportHeight = Math.round(containerHeight / density);

                                                    // 移除现有的viewport meta标签
                                                    var existingViewport = document.querySelector('meta[name="viewport"]');
                                                    if (existingViewport) {
                                                        existingViewport.remove();
                                                    }

                                                    // 创建新的viewport meta标签
                                                    var viewport = document.createElement('meta');
                                                    viewport.name = 'viewport';
                                                    viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=no, height=' + viewportHeight;
                                                    document.head.appendChild(viewport);

                                                    // 设置body和html的高度
                                                    document.body.style.height = viewportHeight + 'px';
                                                    document.documentElement.style.height = viewportHeight + 'px';
                                                    document.body.style.overflow = 'visible';

                                                    return 'Viewport设置完成: ' + viewportHeight + 'px';
                                                })();
                                            """.trimIndent()) { result ->
                                                MyLogUtil.Log("1111", "H5推荐区: Viewport设置结果: $result")

                                                // 再次检查页面状态
                                                webView.evaluateJavascript("""
                                                    (function() {
                                                        var body = document.body;
                                                        var html = document.documentElement;
                                                        var height = Math.max(
                                                            body.scrollHeight, body.offsetHeight,
                                                            html.clientHeight, html.scrollHeight, html.offsetHeight
                                                        );
                                                        var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                                                        var clientHeight = window.innerHeight;
                                                        return JSON.stringify({
                                                            contentHeight: height,
                                                            scrollTop: scrollTop,
                                                            clientHeight: clientHeight,
                                                            canScroll: height > clientHeight
                                                        });
                                                    })();
                                                """.trimIndent()) { finalResult ->
                                                    MyLogUtil.Log("1111", "H5推荐区: 最终页面状态: $finalResult")
                                                }
                                            }
                                        }
                                    } catch (e: Exception) {
                                        MyLogUtil.Log("1111", "H5推荐区: UI线程高度调整异常: ${e.message}")
                                    }
                                }
                            }

                            handler.complete("{\"code\":0,\"data\":null}")
                        } catch (e: Exception) {
                            MyLogUtil.Log("1111", "H5推荐区: 高度调整异常: ${e.message}")
                            handler.complete("{\"code\":0,\"data\":null}")
                        }
                    }
                    webUtil.Type_shareUrl -> {
                        // 处理分享链接，检查是否为商品链接
                        val shareData = Gson().fromJson(msg, com.tbit.uqbike.entity.shareUrlData::class.java)
                        val url = shareData.data.url
                        MyLogUtil.Log("1111", "H5推荐区: 检测到分享链接: $url")

                        if (isProductUrl(url)) {
                            // 商品链接，打开商品详情页面
                            MyLogUtil.Log("1111", "H5推荐区: 识别为商品链接，打开商品详情页面")
                            openProductDetail(url)
                            handler.complete("{\"code\":0,\"data\":null}")
                        } else {
                            // 普通分享链接，执行分享操作
                            MyLogUtil.Log("1111", "H5推荐区: 普通分享链接，执行分享操作")
                            webUtil.shareText(activity as BaseActivity, "", shareData.data.text + "\n" + shareData.data.url)
                            handler.complete("{\"code\":0,\"data\":null}")
                        }
                    }
                    webUtil.Type_goWebview -> {
                        // 处理页面跳转请求
                        val webData = Gson().fromJson(msg, com.tbit.uqbike.entity.goWebData::class.java)
                        val url = webData.data.url
                        MyLogUtil.Log("1111", "H5推荐区: 检测到页面跳转请求: $url")

                        if (isProductUrl(url)) {
                            // 商品链接，打开商品详情页面
                            MyLogUtil.Log("1111", "H5推荐区: 识别为商品链接，打开商品详情页面")
                            openProductDetail(url)
                        } else {
                            // 普通页面跳转
                            MyLogUtil.Log("1111", "H5推荐区: 普通页面跳转")
                            val title = if (webData.data.title.isNullOrEmpty()) "" else webData.data.title
                            activity?.startActivity<WebActionActivity>(
                                WebActionActivity.TITLE to title,
                                WebActionActivity.URL to url,
                                WebActionActivity.IsHideHead to title.isEmpty()
                            )
                        }
                        handler.complete("{\"code\":0,\"data\":null}")
                    }
                    else -> {
                        // 其他类型的JavaScript调用，使用通用处理器
                        MyLogUtil.Log("1111", "H5推荐区: 使用通用处理器处理: ${resultData.type}")
                        webUtil.goJsMethod(activity as BaseActivity, handler, msg!!)
                    }
                }
            } else {
                MyLogUtil.Log("1111", "H5推荐区: JavaScript数据解析失败")
                handler.complete("{\"code\":-1,\"data\":\"数据解析失败\"}")
            }
        } catch (e: Exception) {
            MyLogUtil.Log("1111", "H5推荐区: JavaScript处理异常: ${e.message}")
            e.printStackTrace()
            handler.complete("{\"code\":-1,\"data\":\"处理异常\"}")
        }
    }

    // 判断是否为商品链接
    private fun isProductUrl(url: String): Boolean {
        return url.contains("/product/") ||
               url.contains("/goods/") ||
               url.contains("/item/") ||
               url.contains("product_id=") ||
               url.contains("goods_id=") ||
               url.contains("item_id=")
    }

    // 打开商品详情页面
    private fun openProductDetail(url: String) {
        try {
            MyLogUtil.Log("1111", "H5推荐区: 打开商品详情页面: $url")
            activity?.startActivity<WebActionActivity>(
                WebActionActivity.TITLE to "",
                WebActionActivity.URL to url,
                WebActionActivity.IsHideHead to true
            )
        } catch (e: Exception) {
            MyLogUtil.Log("1111", "H5推荐区: 打开商品详情页面异常: ${e.message}")
            e.printStackTrace()
        }
    }

    // 计算H5推荐区域的合适固定高度
    private fun calculateH5RecommendHeight(context: android.content.Context): Int {
        val displayMetrics = context.resources.displayMetrics
        val screenHeight = displayMetrics.heightPixels
        val screenWidth = displayMetrics.widthPixels
        val density = displayMetrics.density

        // 基础高度：根据屏幕尺寸动态计算
        val baseHeightDp = when {
            // 小屏设备 (< 5.5英寸) - 通常是1920x1080或更小
            screenHeight < 1920 -> 380
            // 中等屏幕设备 (5.5-6.5英寸) - 通常是1920x1080到2400x1080
            screenHeight < 2400 -> 420
            // 大屏设备 (> 6.5英寸) - 通常是2400x1080或更大
            else -> 480
        }

        // 根据屏幕密度调整
        val densityFactor = when {
            density <= 1.5f -> 0.9f  // ldpi, mdpi
            density <= 2.0f -> 1.0f  // hdpi
            density <= 3.0f -> 1.1f  // xhdpi, xxhdpi
            else -> 1.2f             // xxxhdpi
        }

        // 根据屏幕宽高比调整（考虑全面屏等特殊比例）
        val aspectRatio = screenHeight.toFloat() / screenWidth.toFloat()
        val aspectFactor = when {
            aspectRatio > 2.1f -> 1.2f   // 超长屏幕（如21:9）
            aspectRatio > 1.8f -> 1.15f  // 全面屏（如19.5:9, 18:9）
            else -> 1.0f                 // 传统16:9屏幕
        }

        // 考虑内容复杂度的额外调整
        val contentFactor = 1.1f  // H5推荐内容通常包含多个商品，需要额外空间

        val finalHeightDp = (baseHeightDp * densityFactor * aspectFactor * contentFactor).toInt()
        val finalHeightPx = (finalHeightDp * density).toInt()

        // 设置最小和最大高度限制
        val minHeightPx = (300 * density).toInt()  // 最小300dp
        val maxHeightPx = (screenHeight * 0.6f).toInt()  // 增加到屏幕高度的60%，给H5内容更多空间

        // 确保最小值不大于最大值
        val safeMinHeightPx = minOf(minHeightPx, maxHeightPx)
        val constrainedHeight = finalHeightPx.coerceIn(safeMinHeightPx, maxHeightPx)

        MyLogUtil.Log("1111", "H5推荐区: 屏幕信息 - 高度:${screenHeight}px, 宽度:${screenWidth}px, 密度:${density}, 比例:${String.format("%.2f", aspectRatio)}")
        MyLogUtil.Log("1111", "H5推荐区: 高度计算 - 基础:${baseHeightDp}dp, 密度系数:${densityFactor}, 比例系数:${aspectFactor}, 内容系数:${contentFactor}")
        MyLogUtil.Log("1111", "H5推荐区: 高度限制 - 最小:${safeMinHeightPx}px(${(safeMinHeightPx/density).toInt()}dp), 最大:${maxHeightPx}px(${(maxHeightPx/density).toInt()}dp)")
        MyLogUtil.Log("1111", "H5推荐区: 计算结果 - 原始:${finalHeightDp}dp(${finalHeightPx}px), 约束后:${(constrainedHeight/density).toInt()}dp(${constrainedHeight}px)")

        return constrainedHeight
    }

    // 测试H5页面可访问性
    private fun testH5PageAccessibility(url: String) {
        try {
            MyLogUtil.Log("1111", "H5推荐区: 测试页面可访问性: $url")
            // 这里可以添加网络请求测试页面是否可访问
            // 暂时只记录日志，实际的网络测试可以根据需要添加
        } catch (e: Exception) {
            MyLogUtil.Log("1111", "H5推荐区: 测试页面可访问性异常: ${e.message}")
        }
    }

    // 重新加载H5推荐区域
    private fun reloadH5RecommendArea() {
        try {
            val h5Area = MyView?.findViewById<FrameLayout>(R.id.layout_h5_area)
            if (h5Area != null && h5Area.childCount > 0) {
                val webView = h5Area.getChildAt(0) as? DWebView
                if (webView != null) {
                    MyLogUtil.Log("1111", "H5推荐区: 重新加载页面")

                    // 检查GOSHOP_WEB_HOST是否配置
                    val goshopHost = try {
                        FlavorConfig.NET.GOSHOP_WEB_HOST
                    } catch (e: Exception) {
                        MyLogUtil.Log("1111", "H5推荐区: GOSHOP_WEB_HOST未配置，使用默认值")
                        "https://www.gogo-shop.com"
                    }

                    if (goshopHost.isNullOrBlank()) {
                        MyLogUtil.Log("1111", "H5推荐区: GOSHOP_WEB_HOST为空，无法重新加载")
                        return
                    }

                    val url = "$goshopHost/app/wealth/tao_recommend?locale=" + FlavorConfig.Local.language
                    testH5PageAccessibility(url)

                    val headers = mutableMapOf<String, String>()
                    headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
                    headers["Accept-Language"] = FlavorConfig.Local.language
                    headers["Cache-Control"] = "no-cache"
                    webView.loadUrl(url, headers)
                }
            }
        } catch (e: Exception) {
            MyLogUtil.Log("1111", "H5推荐区: 重新加载异常: ${e.message}")
            e.printStackTrace()
        }
    }

    // 确保H5推荐区域布局稳定
    private fun ensureH5AreaStable() {
        try {
            val h5Area = MyView?.findViewById<FrameLayout>(R.id.layout_h5_area)
            if (h5Area != null && h5Area.childCount > 0) {
                val webView = h5Area.getChildAt(0) as? DWebView
                if (webView != null) {
                    // 确保WebView有正确的标记
                    webView.tag = "h5_recommend_webview"

                    // 如果高度已经被动态调整，则不再覆盖
                    if (h5RecommendHeightAdjusted) {
                        MyLogUtil.Log("1111", "H5推荐区: 高度已被动态调整，跳过自动高度设置")
                        return
                    }

                    // 计算合适的固定高度
                    val fixedHeight = calculateH5RecommendHeight(requireContext())
                    val layoutParams = webView.layoutParams
                    if (layoutParams.height != fixedHeight) {
                        layoutParams.height = fixedHeight
                        webView.layoutParams = layoutParams
                        MyLogUtil.Log("1111", "H5推荐区: 重新设置智能计算的固定高度 ${fixedHeight}px")
                    }

                    // 禁用WebView的滚动
                    webView.overScrollMode = View.OVER_SCROLL_NEVER
                    webView.isVerticalScrollBarEnabled = false
                    webView.isHorizontalScrollBarEnabled = false

                    // 检查WebView是否加载失败，如果失败则重新加载
                    if (webView.url.isNullOrEmpty() || webView.url == "about:blank") {
                        MyLogUtil.Log("1111", "H5推荐区: 检测到WebView未正确加载，尝试重新加载")
                        reloadH5RecommendArea()
                    }
                }
            }
        } catch (e: Exception) {
            MyLogUtil.Log("1111", "H5推荐区: 确保布局稳定异常: ${e.message}")
            e.printStackTrace()
        }
    }

    // 广告数据处理
    fun getbottomAdData(){
        ComModel.getAd(Glob.area_id, ComModel.TYPE_AD_4).subscribeBy(
            onNext = {
                MyLogUtil.Log("AD_DEBUG", "广告接口返回: $it")
                val resultData = Gson().fromJson(it.toString(), getAdData::class.java)
                
                val adImageIds = arrayOf(R.id.im_image0, R.id.im_image1, R.id.im_image2, R.id.im_image3, R.id.im_image4)
                
                if (resultData.size > 0){
                    val listKey = ArrayList<String>()
                    val listLink_url = ArrayList<String>()
                    val listTitle = ArrayList<String>()
                    val listIs_show_app_header = ArrayList<Int>()
                    
                    resultData.forEach {
                        if (it.images.size > 0) listKey.add(it.images[0])
                        listLink_url.add(it.link_url)
                        listTitle.add(it.title)
                        listIs_show_app_header.add(it.is_show_app_header!!)
                    }

                    adImageIds.forEachIndexed { index, imageId ->
                        val imageView = MyView?.findViewById<ImageView>(imageId)
                        if (index < listKey.size) {
                            imageView?.visibility = View.VISIBLE
                            ImageLoad.loadimgWithCorner(listKey[index], imageView!!, 10)
                            imageView.setOnClickListener {
                                val link = UrlDecodeUtil().getParm(listLink_url[index])
                                AppUtil.goAdAciton(activity!!, listTitle[index], AppUtil.parseLink(link)!!, listIs_show_app_header[index], 1)
                            }
                        } else {
                            imageView?.visibility = View.GONE
                        }
                    }
                } else {
                    adImageIds.forEach { imageId ->
                        MyView?.findViewById<ImageView>(imageId)?.visibility = View.GONE
                    }
                }

                val adArea = MyView?.findViewById<LinearLayout>(R.id.layout_ad_area)
                val adImgs = adImageIds.map { MyView?.findViewById<ImageView>(it) }
                val allAdsGone = adImgs.all { it?.visibility == View.GONE }
                adArea?.visibility = if (allAdsGone) View.GONE else View.VISIBLE
                MyLogUtil.Log("AD_DEBUG", "layout_ad_area visibility: ${adArea?.visibility}")
            }
        ).toCancelable()
    }

    // 工具方法
    fun goScan(){
        MyView?.findViewById<RelativeLayout>(R.id.rl_scanner)?.performClick()
    }
    
    private fun goToScanBorrow() {
        activity?.startActivity(ScanForBorrowActivity.createIntent(activity!!, ScanForBorrowActivity.TYPE_CAR))
    }
    
    fun IsAuth() : Boolean{
        if(SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            activity?.startActivity<AuthActivity>()
            activity?.finish()
            return false
        }
        return true
    }
    
    @RequiresApi(Build.VERSION_CODES.M)
    fun AutoTask(){
        mainAdFragment.setTopState()
        getRideState()
        // 确保H5推荐区域在自动刷新时保持稳定
        ensureH5AreaStable()
    }

    // 骑行状态管理
    private var isFirstOrder = false
    var isFirstGetOrderState = true
    
    // EventBus事件处理
    @RequiresApi(Build.VERSION_CODES.M)
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        MyLogUtil.Log("1111", "==== event message ==="+event.code+","+event.msg)
        if(event.code == EventUtil.EVENT_NOTITY){
            val resultData: CarInfoData = Gson().fromJson(event.msg, CarInfoData::class.java)
            MyView?.postDelayed({
                if (isFirstOrder && !Glob.isRental){
                    activity?.startActivity<UserCarActivity>(
                        UserCarActivity.TYPE to UserCarActivity.TYPE_NEW,
                        UserCarActivity.UNIT to "")
                }
            },1000)
        }else if(event.code == EventUtil.EVENT_LOGINOUT){
            if (Glob.isRental){
                MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.scan_lease)
            }else{
                MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(com.tbit.uqbike.R.string.scan_unlock)
            }
            setBomRidingView(false,false)
        }else if(event.code == EventUtil.EVENT_RIDINGINFO){
            MyView?.postDelayed({
            },800)
        }else if(event.code == EventUtil.EVENT_LOCAL){
            MyLogUtil.Log("4444","====定位授权 main========")
            LocationModel.onRequestPermissionSuccess()
            (activity as HomeActivity).getMapData()
        } else if(event.code == EventUtil.EVENT_INFOVIEW){
            MyLogUtil.Log("1111","====infoview click========")
        } else if(event.code == EventUtil.EVENT_GORIDE){
            startActivity<MainActivity>()
        }else if (event.code == EventUtil.EVENT_LEASESUC){
            isFirstGetOrderState = true
        }
        if(event.code == EventUtil.EVENT_EXIT) activity!!.finish()
    }
    @RequiresApi(Build.VERSION_CODES.M)
    fun onRestar(){
        try {
            (activity as HomeActivity).getMapData()
            mainAdFragment.onRestart()
        }catch (e : NullPointerException){}
    }

    override fun onResume() {
        super.onResume()
        mainAdFragment.setHeadImg()

        // 恢复时检查并触发广告加载
        tryTriggerAdLoad("onResume")

        // 确保H5推荐区域在页面恢复时保持稳定
        ensureH5AreaStable()
    }
    private fun onSearchCenterChange() {
        loadData()
    }
    private fun loadData() {
        publish.onNext(1)
    }

    var oldLatlng = LatLng()
    var isMoveMapFirst = true
    private fun loadDataImpl() {
        val latLng = mainMapFragment.searchCenter
        MyLogUtil.Log("4444","地图移动=："+latLng.lat+","+latLng.lng)
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 10){
                mainMapFragment.IsOutArea()
            }
            MyLogUtil.Log("1111","地图移动====================："+isMoveMapFirst)
            MyView?.findViewById<RelativeLayout>(R.id.rl_map)?.postDelayed({isMoveMapFirst = false},2000)
            if(!isMoveMapFirst){
                if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 50){
                    oldLatlng = latLng
                    bikeMapDelegate.clear()
                    bikeMapDelegate.select(null)
                    parkPointMapDelegate.clear()
                    parkPointMapDelegate.select(null)
                    prohibitAreaMapDelegate.clear()
                    prohibitAreaMapDelegate.select(null)
                    getNearGeo()
                    getNearParkPointsAndProhibits()
                    getBikes()
                }
            }
        }
    }
    private fun getNearGeo() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearGeo(latLng.lat, latLng.lng, 5000)
    }

    // 获取主页数据 及设置
    @RequiresApi(Build.VERSION_CODES.M)
    fun getMainData(){
        mainMapFragment.sumitLocal(false)
        getNearGeo()
        getNearParkPointsAndProhibits()
        getBikes()
        mainBusinessFragment.sumitEvent(mainMapFragment.getLocation())
        mainMapFragment.IsOutArea()

        if (Glob.isLogin) {
            getRideState()

            ComModel.getNewRegister().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取新手奖励 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), RigisterData::class.java)
                    if (resultData != null){
                        if (resultData.is_display){
                            var hintData = ""
                            var listData = ArrayList<Coupon>()
                            if (resultData.type == RIG_TYPE_NEWUSER){
                                if (resultData.present_amount != 0){
                                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSER,resultData.present_amount.toString())
                                    hintData = getString(R.string.s_get) + AppUtil.formatAmount(resultData.present_amount.toLong())+Glob.CurrencyUnit+ getString(R.string.s_ridemoney)
                                    listData.add(
                                        Coupon(0,0f,0,0f,
                                        AppUtil.formatAmount(resultData.present_amount.toLong())+Glob.CurrencyUnit+ getString(R.string.s_ridemoney),0,
                                        0,0,false,
                                            type_coupon_money,"")
                                    )
                                }
                                if (resultData!!.ride_card != null){
                                    if (resultData.ride_card!!.number > 0){
                                        if (hintData.isNullOrEmpty()){
                                            hintData = getString(R.string.s_get) + getString(R.string.s_get_ridecard,resultData.ride_card.number.toString())
                                        }else{
                                            hintData = hintData + "、"+ getString(R.string.s_get_ridecard,resultData.ride_card.number.toString())
                                        }
                                        listData.add(
                                            Coupon(resultData.ride_card.ride_card_id,0f,0,0f,
                                            resultData.ride_card.name,0, 0,0,false,
                                                type_coupon_ridecard,"")
                                        )
                                    }
                                }
                            }
                            if(resultData.coupon.size > 0){
                                if (hintData.isNullOrEmpty()){
                                    hintData = getString(R.string.s_get) + getString(R.string.s_get_invite,resultData.coupon.size.toString())
                                }else{
                                    hintData = hintData + "、"+ getString(R.string.s_get_invite,resultData.coupon.size.toString())
                                }
                                resultData.coupon.forEach {
                                    listData.add(
                                        Coupon(it.user_coupon_id,it.amount,it.is_min_spend,it.min_spend_amount, it.name,it.end_time,
                                        0,it.type,false,
                                            type_coupon_invite,"")
                                    )
                                }
                            }

                            var inviteDialog = InviteDialog.newInstance(hintData)
                            if (resultData.type == RIG_TYPE_NEWUSER){
                                inviteDialog.type = InviteDialog.TYPE_NEWUSER
                            }else{
                                inviteDialog.type = InviteDialog.TYPE_INVITE
                            }
                            inviteDialog.listData = listData
                            lifecycleDialogHelper.show(inviteDialog)
                        }
                    }
                },
                onError = {}
            ).toCancelable()
        }

    }

    fun moveUp(){
        (activity as HomeActivity).setViewPageMove(true)
        MyView?.findViewById<CustomNestedScrollView>(R.id.nscl)?.setEnableScroll(true)
    }


    var MainState = 0
    var MainStateTypeIsRental = false
    var MainOrderState = 1
    var orderNo = ""
    @RequiresApi(Build.VERSION_CODES.M)
    fun getRideState(){
        if (Glob.isLogin){
            if (!NetUtils.isConnected(ContextUtil.getContext())) return
            OrderModel.getMyOrderStatus().subscribeBy(
                onNext = {
                    MyLogUtil.Log("7777","===是否存在未结束的骑行订单 信息=="+it.toString())
                    val resultData: MyOrderStateData = Gson().fromJson(it.toString(), MyOrderStateData::class.java)
                    if (resultData != null){
                        // 骑行订单状态：1进行中，2待支付
                        MainState = resultData.status
                        orderNo = resultData.order_no
                        MainOrderState = resultData.type
                        if (MainOrderState == Constant.OrderType.RENTAL_Y){
                            MainStateTypeIsRental = true
                        }else{
                            MainStateTypeIsRental = false
                        }
                        if (resultData.status == 1 || resultData.status == 2){
                            ridingTime = resultData.order_time
                            ridingPrice = resultData.amount
                        }
                        if(resultData.status == 1){
                            if (isFirstGetOrderState){
                                MyLogUtil.Log("7777","===是否存在未结束的骑行订单 信息=="+isFirstGetOrderState)
                                isFirstGetOrderState = false
                                // 跳转骑行页面
                                startActivity<MainActivity>()
                            }
                            setBomRidingView(true,false)
                        }else{
                            SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_VECHNO, "");

                            if (resultData.status == 2){
                                MyView?.findViewById<ImageView>(R.id.image_scanner)?.visibility = View.GONE
                                MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.s_order_npay)+"("+AppUtil.getFloat2(resultData.amount)+Glob.CurrencyUnit+")"
                                setBomRidingView(true,true)
                            }else{
                                MyView?.findViewById<ImageView>(R.id.image_scanner)?.visibility = View.VISIBLE
                                if (Glob.isRental){
                                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.scan_lease)
                                }else{
                                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(com.tbit.uqbike.R.string.scan_unlock)
                                }
                                setBomRidingView(false,false)
                            }
                        }
                    }
                }
            ).toCancelable()
        }
    }

    var ridingTime = 0L
    var ridingPrice = 0f
    fun setBomRidingView(isRiding : Boolean,isPaying : Boolean){
        if (isRiding){
            MyView?.findViewById<LinearLayout>(R.id.ly_riding_btn)?.visibility = View.VISIBLE
            MyView?.findViewById<LinearLayout>(R.id.ly_uriding)?.visibility = View.GONE

            if (isPaying){
                MyView?.findViewById<RoundTextView>(R.id.rv_seeOrder)?.text = getString(R.string.s_gopay)
                if (MainStateTypeIsRental){
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.s_rental_end)
                    var unit = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content = getString(R.string.s_cost_rental_upay)+unit
                    val spannableString = AppUtil.getSpanStr(unit,content,R.color.c_F86125)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }else{
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.s_riding_end)
                    var unit2 = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content2 = getString(R.string.s_cost_upay)+unit2
                    val spannableString = AppUtil.getSpanStr(unit2,content2,R.color.c_F86125)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }
            }else{
                MyView?.findViewById<RoundTextView>(R.id.rv_seeOrder)?.text = getString(R.string.s_see)
                if (MainStateTypeIsRental){
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.s_rentaling)
                    var unit = ridingTime.toString()+getString(R.string.min)
                    var content = getString(R.string.s_rental_has)+unit
                    val spannableString = AppUtil.getSpanStr(unit,content,R.color.blue_namal)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }else{
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.riding)
                    var unit = ridingTime.toString()+getString(R.string.min)
                    var content = getString(R.string.s_riding_has)+unit
                    var unit2 = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content2 = getString(R.string.travel_consumption)+unit2
                    content = content +", "+ content2
                    val spannableString = AppUtil.getSpanStr2(content,unit,unit2,R.color.blue_namal,R.color.blue_namal)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }
            }
        }else{
            MyView?.findViewById<LinearLayout>(R.id.ly_riding_btn)?.visibility = View.GONE
            MyView?.findViewById<LinearLayout>(R.id.ly_uriding)?.visibility = View.VISIBLE
        }
    }

    override fun onDestroy() {
        loadDataDisposable.dispose()
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }

    override fun onGetNearGeoSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                geoMapDelegate.setGeoNew(areaData)
            }
        }
    }
    private fun getNearParkPointsAndProhibits() {
        getNearParkPoints()
        getNearProhibitsImpl(false)
    }
    private fun getNearParkPoints() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearParkPoints(latLng.lat, latLng.lng, 500)
    }
    override fun onGetNearParkPointsSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                parkPointMapDelegate.setPartAreaData(areaData)
            }
        }
    }
    private fun getNearProhibitsImpl(isProhibitMode: Boolean) {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearProhibits(latLng.lat, latLng.lng)
    }
    override fun onGetNearProhibitsSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                prohibitAreaMapDelegate.setProhibitAreaData(areaData)
            }
        }
    }
    private fun getBikes() {
        getNearBikes()
    }
    private fun getNearBikes() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearBikes(latLng.lat, latLng.lng, 500)
    }
    override fun onGetNearBikesSuccess(resultData: VehicleData) {
        bikeMapDelegate.setBickData(resultData)
    }
    override fun onGetPhoneInfoSuccess(phoneInfoList: List<PhoneInfo>?) {}
    override fun onGetParkPointInfoSuccess(resultData: ParkData) {}
    override fun onGetDepositSuccess(adDeposit: AdDeposit?) {}
    override fun onGetUnreadMessageCount(unreadCount: Int) {}
    override fun onGoogleNetNotAvailable() {}
    override fun showErrMsg(message: String) {}

    fun onAreaIdSucListener(callSource: String){
        if (Glob.area_id == 0L) return

        // 保存区域ID
        pendingAreaId = Glob.area_id

        // 区域ID可用时调用检查函数
        tryTriggerAdLoad("onAreaIdSucListener_$callSource")
    }

    fun onAreaIdFailListener(callSource: String){
        // 失败时清除待处理的ID
        pendingAreaId = null

        // 可选：仍然触发子Fragment中的getData来清除其状态
        if (mainAdFullFragment?.isAdded == true) {
            mainAdFullFragment?.getData("${callSource}_fail")
        }
    }

    // 检查条件并触发广告加载的辅助函数
    private fun tryTriggerAdLoad(callSource: String) {
        if (pendingAreaId != null && mainAdFullFragment?.isAdded == true) {
            mainAdFullFragment?.getData("HomeFrag_$callSource") // 传递具体来源
            pendingAreaId = null // 使用后清除待处理的ID
        }
    }

}