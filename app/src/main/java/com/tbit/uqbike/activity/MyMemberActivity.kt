package com.tbit.uqbike.activity

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.viewpager2.widget.ViewPager2
import com.cn.maintenance.network.UserRClient
import com.google.gson.Gson
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.BanelMemberAdapter
import com.tbit.uqbike.adapter.HomePageAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.base.BaseFragment
import com.tbit.uqbike.bean.MemberInfoResponse
import com.tbit.uqbike.bean.CouponItem
import com.tbit.uqbike.bean.Coupon
import com.tbit.uqbike.databinding.ActivityMyMemberBinding
import com.tbit.uqbike.entity.CustomBean
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.fragment.Member1Frag
import com.tbit.uqbike.fragment.Member2Frag
import com.tbit.uqbike.fragment.Member3Frag
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.zhpan.bannerview.BannerViewPager
import com.zhpan.bannerview.constants.PageStyle
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.schedulers.Schedulers
import org.jetbrains.anko.startActivity
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// ===================== 会员中心页面 Activity =====================
class MyMemberActivity : BaseActivity() {
    // 视图绑定
    private lateinit var binding: ActivityMyMemberBinding
    // 顶部会员卡片轮播控件
    private lateinit var mViewPager: BannerViewPager<CustomBean>
    // 底部权益详情ViewPager适配器
    private var adapters: HomePageAdapter? = null
    // Fragment数组，分别对应三大权益
    private var type:Array<BaseFragment>? = null
    // 三大权益Fragment
    private lateinit var member1Frag: Member1Frag // 专属券包
    private lateinit var member2Frag: Member2Frag // 非P免罚
    private lateinit var member3Frag: Member3Frag // 骑行卡延时
    // 顶部会员卡片数据源
    var banelData = ArrayList<CustomBean>() // This will be populated from API

    // RxJava资源管理
    private lateinit var compositeDisposable: CompositeDisposable
    // 当前所有会员等级的权益数据（后端返回）
    private var memberInfoListGlobal: List<MemberInfoResponse> = emptyList()

    // 会员卡片所需图片资源封装
    private data class MemberImageResources(val mainImage: Int, val hg: Int, val bg: Int, val ques: Int, val go: Int, val qbIcon: Int, val sIcon: Int? = null, val qyL: Int, val qyR: Int)

    // 获取不同会员等级对应的权益标题资源ID
    private fun getMemberLevelTitleResId(memberLevel: Int): Int {
        return when (memberLevel) {
            1 -> R.string.s_vip_bronze_equity
            2 -> R.string.s_vip_sliver_equity
            3 -> R.string.s_vip_gold_equity
            4 -> R.string.s_vip_platinum_equity
            5 -> R.string.s_vip_blackgold_equity
            else -> R.string.app_name // Use a guaranteed existing string as fallback
        }
    }

    // 获取不同会员等级对应的卡片图片资源
    private fun getMemberImageResources(level: Int?): MemberImageResources {
        return when (level) {
            1 -> MemberImageResources(R.drawable.img_qt, R.drawable.img_qt_hg, R.drawable.img_qt_bg, R.drawable.ic_more_qt, R.drawable.ic_go_qt, R.drawable.ic_qt_qb, R.drawable.ic_s_qt, R.drawable.ic_zs_qt, R.drawable.ic_zs_qt1) // 青铜
            2 -> MemberImageResources(R.drawable.img_by, R.drawable.img_by_hg, R.drawable.img_by_bg, R.drawable.ic_more_by, R.drawable.ic_go_by, R.drawable.ic_by_qb, R.drawable.ic_s_bj, R.drawable.ic_zs_by, R.drawable.ic_zs_by1) // 白银
            3 -> MemberImageResources(R.drawable.img_hj, R.drawable.img_hj_hg, R.drawable.img_hj_bg, R.drawable.ic_more_hj, R.drawable.ic_go_hj, R.drawable.ic_hj_qb, R.drawable.ic_s_hj, R.drawable.ic_zs_hj, R.drawable.ic_zs_hj1) // 黄金
            4 -> MemberImageResources(R.drawable.img_bj, R.drawable.img_bj_hg, R.drawable.img_bj_bg, R.drawable.ic_more_bj, R.drawable.ic_go_bj, R.drawable.ic_bj_qb, R.drawable.ic_s_bj, R.drawable.ic_zs_bj, R.drawable.ic_zs_bj1) // 白金
            5 -> MemberImageResources(R.drawable.img_blackj, R.drawable.img_blackj_hg, R.drawable.img_blackj_bg, R.drawable.ic_more_blackj, R.drawable.ic_go_blackj, R.drawable.ic_blackj_qb, R.drawable.ic_s_blackj, R.drawable.ic_blackj_jt, R.drawable.ic_blackj_jt1) // 黑金
            else -> MemberImageResources(R.drawable.img_qt, R.drawable.img_qt_hg, R.drawable.img_qt_bg, R.drawable.ic_more_qt, R.drawable.ic_go_qt, R.drawable.ic_qt_qb, R.drawable.ic_s_qt, R.drawable.ic_zs_qt, R.drawable.ic_zs_qt1) // Default
        }
    }

    // 获取不同会员等级对应的非P免罚图标
    private fun getNonParkingIconForLevel(memberLevel: Int?): Int {
        return when (memberLevel) {
            1 -> R.drawable.ic_fp_qt
            2 -> R.drawable.ic_fp_by
            3 -> R.drawable.ic_fp_hj
            4 -> R.drawable.ic_fp_bj
            5 -> R.drawable.ic_fp_blackj
            else -> 0 // Return 0 or a default placeholder if level is unknown/null
        }
    }

    // 获取权益解锁状态对应的字符串资源ID
    private fun getUnlockStatusStringForLevel(memberLevel: Int?, isEquityLocked: Boolean): Int {
        return if (!isEquityLocked) {
            R.string.s_vip_unlock_rights
        } else {
            when (memberLevel) {
                1 -> R.string.s_vip_bronze_equity_use
                2 -> R.string.s_vip_sliver_equity_use
                3 -> R.string.s_vip_gold_equity_use
                4 -> R.string.s_vip_platinum_equity_use
                5 -> R.string.s_vip_blackgold_equity_use
                else -> 0 // Or a generic "Locked" string resource
            }
        }
    }

    // 将后端返回的券包权益数据转为Coupon对象列表
    private fun mapCouponItemsToCoupons(couponItems: List<CouponItem>?, isEquityLocked: Boolean): List<Coupon> {
        if (couponItems == null) {
            Log.d("MyMemberActivity", "券包数据为null")
            return emptyList()
        }
        
        Log.d("MyMemberActivity", "开始转换券包数据，共${couponItems.size}个券包，权益锁定状态: $isEquityLocked")
        
        val context = ContextUtil.getContext()
        return couponItems.mapNotNull mapItems@ { item: CouponItem ->
            Log.d("MyMemberActivity", """
                处理券包数据:
                - userCouponId: ${item.userCouponId}
                - couponId: ${item.couponId}
                - name: ${item.couponInfo?.name}
                - status: ${item.status}
                - endTime(原始): ${item.endTime}
                - startTime: ${item.startTime}
                - type: ${item.type}
                - amount: ${item.amount}
                - isEquityLocked: $isEquityLocked
            """.trimIndent())
            
            val title = item.couponInfo?.name
            val couponUserId = item.userCouponId ?: 0
            val couponId = item.couponId ?: 0
            val itemType = item.type
            val internalStatus = item.status ?: 0
            
            // 时间戳处理优化
            val originalEndTime = item.endTime ?: 0L
            val endTimeLongValue = if (originalEndTime == 0L) {
                Log.w("MyMemberActivity", "券包 ${title} 的endTime为0，这可能是新发放的券包")
                0L // 保持为0，由CouponAdapter根据status判断
            } else {
                // 检查时间戳单位（秒 vs 毫秒）
                val timestampInMillis = if (originalEndTime < 1000000000000L) { // 小于这个值通常是秒级时间戳
                    originalEndTime * 1000L
                } else {
                    originalEndTime // 已经是毫秒级
                }
                Log.d("MyMemberActivity", "券包 ${title} endTime转换: $originalEndTime -> $timestampInMillis")
                timestampInMillis
            }

            var couponMoneyValue = 0f
            var couponTimeValue = 0

            // 优惠券金额逻辑调整
            if (itemType == 1) { // 金额券
                couponMoneyValue = item.couponInfo?.couponAmount?.toFloatOrNull() ?: 0f
                Log.d("MyMemberActivity", "金额券 ${title}: ${couponMoneyValue}")
            } else if (itemType == 2) { // 时长券
                couponTimeValue = item.amount?.toInt() ?: 0
                Log.d("MyMemberActivity", "时长券 ${title}: ${couponTimeValue}分钟")
            }

            val thresholdDescription = when (item.isMinSpend) {
                0 -> context.getString(R.string.s_no_threshold)
                1 -> context.getString(R.string.s_invite_ismin, item.minSpendAmount)
                else -> "" 
            }

            val coupon = Coupon(
                couponUserId = couponUserId,
                couponId = couponId,
                userId = 0, 
                name = title,
                couponMoney = couponMoneyValue,  // 直接使用浮点数值，不进行整数转换
                couponTime = couponTimeValue,
                couponType = itemType ?: 0,
                thresholdDescription = thresholdDescription,
                remark = item.couponInfo?.desc,
                internalStatus = internalStatus,
                endTimeLong = endTimeLongValue,
                isLocked = isEquityLocked
            )
            
            Log.d("MyMemberActivity", "成功转换券包: ${title}, 最终状态 - isLocked: $isEquityLocked, status: $internalStatus, endTime: $endTimeLongValue")
            coupon
        }
    }

    /**
     * Activity生命周期：页面初始化，设置监听，加载数据
     */
    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMyMemberBinding.inflate(layoutInflater)
        setContentView(binding.root)

        compositeDisposable = CompositeDisposable()

        // 顶部关闭按钮
        binding.ivClose.setOnClickListener { finish() }
        // 滚动监听，动态调整顶部栏背景色
        binding.nsv.setTag("0")
        binding.nsv.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            if (scrollY > CommonUtils.dp2px(ContextUtil.getContext(), 80) && binding.nsv.tag.equals("0")){
                binding.nsv.setTag("1")
                binding.rvMemberTitle.setBackgroundColor(resources.getColor(R.color.white))
            }else if (scrollY < CommonUtils.dp2px(ContextUtil.getContext(), 80) && binding.nsv.tag.equals("1")){
                binding.nsv.setTag("0")
                binding.rvMemberTitle.setBackgroundColor(resources.getColor(R.color.transparent))
            }
        }

        // 初始化顶部会员卡片轮播
        setupViewPager() // Initializes mViewPager with adapter etc.

        // 初始化底部权益详情ViewPager及Fragment
        member1Frag = Member1Frag()
        member2Frag = Member2Frag()
        member3Frag = Member3Frag()
        type = arrayOf(member1Frag, member2Frag, member3Frag)
        adapters = HomePageAdapter(this@MyMemberActivity, type!!)
        binding.vp.adapter = adapters
        binding.vp.offscreenPageLimit = 3
        binding.vp.isUserInputEnabled = false // User cannot swipe

        // 设置底部权益选项卡点击事件（优化：每次切换会员卡片时动态绑定）
        // refreshTabClickListeners() 方法已在下面定义

        // 加载会员权益数据（后端接口）
        loadMemberInfoData() // This should populate memberInfoListGlobal and trigger onPageSelected for mViewPager

        // 顶部会员卡片切换监听，切换时刷新权益内容
        mViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                // 切换会员卡片时，刷新权益内容和UI
                if (banelData.isEmpty() || position >= banelData.size || memberInfoListGlobal.isEmpty() || position >= memberInfoListGlobal.size) {
                    // 数据未准备好，清空Fragment内容
                    if (binding.vp.currentItem == 0 && ::member1Frag.isInitialized) {
                        member1Frag.updateCoupons(emptyList())
                    }
                    // 可扩展：清空其他Fragment内容
                    return
                }

                val selectedMemberCustomBean = banelData[position]
                val currentMemberInfo = memberInfoListGlobal[position] // 当前会员等级权益数据
                val images = getMemberImageResources(selectedMemberCustomBean.memberLevel)

                // 切换会员卡片背景、装饰、权益标题等
                ImageLoad.loadimg(resources.getDrawable(images.bg), binding.imgMemberBg)
                ImageLoad.loadimg(resources.getDrawable(images.qyL), binding.imgQyL)
                ImageLoad.loadimg(resources.getDrawable(images.qyR), binding.imgQyR)
                
                val titleResId = getMemberLevelTitleResId(currentMemberInfo.memberLevel ?: 0)
                binding.tvMemberQy.text = getString(titleResId)

                // 首先更新权益选项卡的可见性
                updateEquityTabsVisibility(currentMemberInfo)
                
                // 更新权益数量
                val equity = currentMemberInfo.equity
                equity?.let { equityData ->
                    // 专属券包数量
                    val couponList = equityData.coupons?.list
                    val isCouponsLocked = equityData.coupons?.isLock ?: true
                    val couponsForFragment = mapCouponItemsToCoupons(couponList, isCouponsLocked)
                    if (binding.vp.currentItem == 0 && ::member1Frag.isInitialized) {
                         member1Frag.updateCoupons(couponsForFragment)
                    }
                    
                    val couponCount = couponList?.size ?: 0
                    binding.tvMemberZsqbCount.text = "$couponCount${getString(R.string.benefit_unit_piece)}"

                    // 非P免罚次数
                    val fineWaiverCount = equityData.otherPark?.parkImpunity ?: 0
                    binding.tvMemberFpmfCount.text = "$fineWaiverCount${getString(R.string.benefit_unit_time)}"
                    
                    // 骑行卡延时次数
                    val rideCardExtensionTimes = equityData.rideCard?.rideCardDelay ?: 0
                    binding.tvMemberQxkCount.text = "$rideCardExtensionTimes${getString(R.string.min)}"

                    // 根据权益锁定状态显示锁图标
                    binding.imgMemberZsqbS.visibility = if (isCouponsLocked) View.VISIBLE else View.GONE
                    binding.imgMemberFpmfS.visibility = if (equityData.otherPark?.isLock == true) View.VISIBLE else View.GONE
                    val rideCardEquity = equityData.rideCard
                    binding.imgMemberQxkS.visibility = if (rideCardEquity != null && (rideCardEquity.rideCardDelay ?: 0) > 0) View.VISIBLE else View.GONE

                    // 设置锁图标
                    if (isCouponsLocked) {
                        ImageLoad.loadimg(resources.getDrawable(images.sIcon ?: R.drawable.ic_s_qt), binding.imgMemberZsqbS)
                    }
                    if (equityData.otherPark?.isLock == true) {
                        ImageLoad.loadimg(resources.getDrawable(images.sIcon ?: R.drawable.ic_s_qt), binding.imgMemberFpmfS)
                    }
                    if (rideCardEquity != null && (rideCardEquity.rideCardDelay ?: 0) > 0) {
                        ImageLoad.loadimg(resources.getDrawable(images.sIcon ?: R.drawable.ic_s_qt), binding.imgMemberQxkS)
                    }
                } ?: run {
                    // 没有权益数据时全部置0，但可见性已经由updateEquityTabsVisibility处理
                    binding.tvMemberZsqbCount.text = "0${getString(R.string.benefit_unit_piece)}"
                    binding.tvMemberFpmfCount.text = "0${getString(R.string.benefit_unit_time)}"
                    binding.tvMemberQxkCount.text = "0${getString(R.string.s_vip_times)}"
                    
                    // 没有权益数据时显示所有锁图标
                    binding.imgMemberZsqbS.visibility = View.VISIBLE
                    binding.imgMemberFpmfS.visibility = View.VISIBLE
                    binding.imgMemberQxkS.visibility = View.VISIBLE
                    
                    // 设置默认锁图标
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_s_qt), binding.imgMemberZsqbS)
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_s_qt), binding.imgMemberFpmfS)
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_s_qt), binding.imgMemberQxkS)
                }

                // 会员卡片装饰图标、权益标题颜色等
                binding.imgMemberZsqbS.visibility = if (images.sIcon != null) View.VISIBLE else View.GONE
                images.sIcon?.let { ImageLoad.loadimg(resources.getDrawable(it), binding.imgMemberZsqbS) }
                ImageLoad.loadimg(resources.getDrawable(images.qbIcon), binding.imgMemberZsqb)

                val textColor = when (selectedMemberCustomBean.memberLevel) {
                    5 -> resources.getColor(R.color.white) // Black Gold
                    else -> resources.getColor(R.color.black_namal)
                }
                binding.tvMember1.setTextColor(textColor) // 专属券包标题
                binding.tvMember2.setTextColor(textColor) // 非P免罚标题
                binding.tvMember3.setTextColor(textColor) // 骑行卡延时标题

                val titleColor = when (selectedMemberCustomBean.memberLevel) {
                    1 -> resources.getColor(R.color.c_5F6B7E)
                    2 -> resources.getColor(R.color.c_439DFF)
                    3 -> resources.getColor(R.color.c_FF9103)
                    4 -> resources.getColor(R.color.c_3F59FF)
                    5 -> resources.getColor(R.color.c_FFA200)
                    else -> resources.getColor(R.color.c_5F6B7E)
                }
                binding.tvMemberQy.setTextColor(titleColor)

                // 刷新权益选项卡的点击监听器
                refreshTabClickListeners()

                // 所有会员等级下，动态设置非P免罚和骑行卡延时图标
                val fpmfIcon = when (selectedMemberCustomBean.memberLevel) {
                    1 -> R.drawable.ic_fp_qt
                    2 -> R.drawable.ic_fp_by
                    3 -> R.drawable.ic_fp_hj
                    4 -> R.drawable.ic_fp_bj
                    5 -> R.drawable.ic_fp_blackj
                    else -> R.drawable.ic_fp_bj
                }
                        ImageLoad.loadimg(resources.getDrawable(fpmfIcon), binding.imgMemberFpmf)

                val qxkIcon = when (selectedMemberCustomBean.memberLevel) {
                    1 -> R.drawable.ic_qxk_qt
                    2 -> R.drawable.ic_qxk_by
                    3 -> R.drawable.ic_qxk_hj
                    4 -> R.drawable.ic_qxk_bj
                    5 -> R.drawable.ic_qxk_blackj
                    else -> R.drawable.ic_qxk_bj
                }
                        ImageLoad.loadimg(resources.getDrawable(qxkIcon), binding.imgMemberQxk)

                // 保持白金、黑金会员的sIcon装饰逻辑
                if (selectedMemberCustomBean.memberLevel == 4 || selectedMemberCustomBean.memberLevel == 5) {
                    images.sIcon?.let { ImageLoad.loadimg(resources.getDrawable(it), binding.imgMemberFpmfS) }
                        images.sIcon?.let { ImageLoad.loadimg(resources.getDrawable(it), binding.imgMemberQxkS) }
                }

                // 切换会员卡片后，强制刷新当前选中的权益详情Fragment
                val currentTabIndex = binding.vp.currentItem
                val firstVisibleTabIndex = getFirstVisibleTabIndex()
                
                Log.d("MyMemberActivity", "会员卡片切换完成 - 当前选项卡: $currentTabIndex, 第一个可见选项卡: $firstVisibleTabIndex")
                
                if (firstVisibleTabIndex >= 0) {
                    // 如果当前选项卡可见，刷新它；否则切换到第一个可见选项卡
                    val targetTabIndex = if (isTabVisible(currentTabIndex)) currentTabIndex else firstVisibleTabIndex
                    selectTab(targetTabIndex)
                } else {
                    // 如果没有任何权益可见，清空Fragment内容
                    Log.w("MyMemberActivity", "No visible equity tabs found for member level ${selectedMemberCustomBean.memberLevel}")
                    if (::member1Frag.isInitialized) member1Frag.updateCoupons(emptyList())
                    if (::member2Frag.isInitialized) member2Frag.updateContent("", 0, true)
                    if (::member3Frag.isInitialized) member3Frag.updateContent("", true)
                }
            }
            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
            }
        })

        // Fragment初始化
        member1Frag = Member1Frag()
        member2Frag = Member2Frag()
        member3Frag = Member3Frag()
        type = arrayOf(member1Frag,member2Frag,member3Frag)
        adapters = HomePageAdapter(this@MyMemberActivity, type!!)
        binding.vp.adapter = adapters
        binding.vp.offscreenPageLimit = 3
        binding.vp.isUserInputEnabled = false
        // 会员规则说明按钮
        binding.tvRule.clickDelay {
            loadingDialogHelper.show {  }
            PageModel.getPageUrl(PageModel.member_rules).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                    startActivity<WebActivity>(WebActivity.TITLE to getString(R.string.s_member_rules),
                        WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                },
                onError = {error ->
                    Log.e("MyMemberActivity", "Error PageModel.getPageUrl", error)
                    loadingDialogHelper.dismiss()
                    Toast.makeText(this, "Failed to load rule: ${error.message}", Toast.LENGTH_SHORT).show()
                }
            ).toCancelable()
        }
        loadMemberInfoData()
    }

    /**
     * 根据会员权益配置更新权益选项卡的可见性
     * @param memberInfo 当前会员等级的权益信息
     */
    private fun updateEquityTabsVisibility(memberInfo: MemberInfoResponse?) {
        val equity = memberInfo?.equity
        
        // 专属券包：有配置且有券包数据才显示
        val hasCoupons = equity?.coupons != null && !equity.coupons.list.isNullOrEmpty()
        binding.lyMemberZsqb.visibility = if (hasCoupons) View.VISIBLE else View.GONE
        
        // 非P免罚：有配置且次数大于0才显示
        val hasOtherPark = equity?.otherPark != null && (equity.otherPark.parkImpunity ?: 0) > 0
        binding.lyMemberFpmf.visibility = if (hasOtherPark) View.VISIBLE else View.GONE
        
        // 骑行卡延时：有配置且延时分钟数大于0才显示
        val hasRideCardDelay = equity?.rideCard != null && (equity.rideCard.rideCardDelay ?: 0) > 0
        binding.lyMemberQxk.visibility = if (hasRideCardDelay) View.VISIBLE else View.GONE
        
        Log.d("MyMemberActivity", "权益可见性更新 - 券包:$hasCoupons, 免罚:$hasOtherPark, 延时:$hasRideCardDelay")
    }

    /**
     * 获取第一个可见的权益选项卡索引
     * @return 可见的权益选项卡索引，如果都不可见则返回-1
     */
    private fun getFirstVisibleTabIndex(): Int {
        return when {
            binding.lyMemberZsqb.visibility == View.VISIBLE -> 0
            binding.lyMemberFpmf.visibility == View.VISIBLE -> 1
            binding.lyMemberQxk.visibility == View.VISIBLE -> 2
            else -> -1
        }
    }

    /**
     * 检查指定索引的权益选项卡是否可见
     * @param index 权益选项卡索引 (0-专属券包, 1-非P免罚, 2-骑行卡延时)
     * @return 是否可见
     */
    private fun isTabVisible(index: Int): Boolean {
        return when (index) {
            0 -> binding.lyMemberZsqb.visibility == View.VISIBLE
            1 -> binding.lyMemberFpmf.visibility == View.VISIBLE
            2 -> binding.lyMemberQxk.visibility == View.VISIBLE
            else -> false
        }
    }

    /**
     * 刷新权益选项卡的点击监听器
     * 只有可见的选项卡才设置点击事件
     */
    private fun refreshTabClickListeners() {
        // 清除所有现有的监听器
        binding.lyMemberZsqb.setOnClickListener(null)
        binding.lyMemberFpmf.setOnClickListener(null)
        binding.lyMemberQxk.setOnClickListener(null)
        
        // 只为可见的选项卡设置监听器
        if (binding.lyMemberZsqb.visibility == View.VISIBLE) {
            binding.lyMemberZsqb.setOnClickListener { selectTab(0) }
        }
        if (binding.lyMemberFpmf.visibility == View.VISIBLE) {
            binding.lyMemberFpmf.setOnClickListener { selectTab(1) }
        }
        if (binding.lyMemberQxk.visibility == View.VISIBLE) {
            binding.lyMemberQxk.setOnClickListener { selectTab(2) }
        }
    }

    /**
     * 选择权益详情选项卡（专属券包、非P免罚、骑行卡延时）
     * @param index 选项卡索引：0-专属券包，1-非P免罚，2-骑行卡延时
     */
    private fun selectTab(index: Int) {
        Log.d("MyMemberActivity", "=== 选择权益选项卡 ===")
        Log.d("MyMemberActivity", "目标选项卡索引: $index")
        Log.d("MyMemberActivity", "当前ViewPager位置: ${mViewPager.currentItem}")
        Log.d("MyMemberActivity", "会员数据列表大小: ${memberInfoListGlobal.size}")
        
        if (memberInfoListGlobal.isEmpty()) {
            Log.w("MyMemberActivity", "会员数据列表为空，无法选择选项卡")
            return
        }

        // 检查索引是否对应可见的选项卡
        if (!isTabVisible(index)) {
            Log.w("MyMemberActivity", "选项卡 $index 不可见，自动选择第一个可见选项卡")
            val firstVisibleIndex = getFirstVisibleTabIndex()
            if (firstVisibleIndex >= 0) {
                selectTab(firstVisibleIndex)
            } else {
                Log.w("MyMemberActivity", "没有可见的选项卡")
            }
            return
        }

        val currentViewPagerPosition = mViewPager.currentItem
        if (currentViewPagerPosition >= memberInfoListGlobal.size) {
            Log.e("MyMemberActivity", "ViewPager位置超出会员数据范围: $currentViewPagerPosition >= ${memberInfoListGlobal.size}")
            return
        }

        val memberInfoResponse = memberInfoListGlobal[currentViewPagerPosition]
        val memberLevel = memberInfoResponse.memberLevel
        Log.d("MyMemberActivity", "当前会员等级: $memberLevel")
        Log.d("MyMemberActivity", "权益数据: ${memberInfoResponse.equity}")

        when (index) {
            0 -> { // 专属券包
                Log.d("MyMemberActivity", "=== 处理专属券包选项卡 ===")
                if (binding.lyMemberZsqb.visibility == View.VISIBLE) {
                val couponList = memberInfoResponse.equity?.coupons?.list
                val isCouponsLocked = memberInfoResponse.equity?.coupons?.isLock ?: true
                val coupons = mapCouponItemsToCoupons(couponList, isCouponsLocked)
                val unlockStatusResId = getUnlockStatusStringForLevel(memberInfoResponse.memberLevel, isCouponsLocked)
                    val equityDesc = memberInfoResponse.equity?.coupons?.desc
                    
                    Log.d("MyMemberActivity", """
                        专属券包数据详情:
                        - 券包列表大小: ${couponList?.size ?: 0}
                        - 是否锁定: $isCouponsLocked
                        - 转换后券包数量: ${coupons.size}
                        - 权益描述: $equityDesc
                        - 解锁状态资源ID: $unlockStatusResId
                    """.trimIndent())
                    
                    // 打印每个券包的详细信息
                    coupons.forEachIndexed { idx, coupon ->
                        Log.d("MyMemberActivity", """
                            券包 $idx: 
                            - 名称: ${coupon.name}
                            - 状态: ${coupon.internalStatus}
                            - 结束时间: ${coupon.endTimeLong}
                            - 是否锁定: ${coupon.isLocked}
                            - 类型: ${coupon.couponType}
                        """.trimIndent())
                    }
                    
                if (::member1Frag.isInitialized) {
                    member1Frag.updateCoupons(coupons)
                    member1Frag.updateUnlockStatusTitle(unlockStatusResId, true)
                        member1Frag.setEquityDesc(equityDesc)
                        Log.d("MyMemberActivity", "已更新Member1Frag数据")
                    } else {
                        Log.w("MyMemberActivity", "Member1Frag未初始化")
                    }
                } else {
                    Log.w("MyMemberActivity", "专属券包选项卡不可见")
                    if (::member1Frag.isInitialized) {
                        member1Frag.updateCoupons(emptyList())
                        member1Frag.updateUnlockStatusTitle(0, false)
                        member1Frag.setEquityDesc("")
                    }
                }
            }
            1 -> { // 非P免罚
                Log.d("MyMemberActivity", "=== 处理非P免罚选项卡 ===")
                if (binding.lyMemberFpmf.visibility == View.VISIBLE) {
                val nonPRules = memberInfoResponse.equity?.otherPark?.desc ?: ""
                val isNonPLocked = memberInfoResponse.equity?.otherPark?.isLock ?: true
                val nonParkingIconResId = getNonParkingIconForLevel(memberInfoResponse.memberLevel)
                val unlockStatusResId = getUnlockStatusStringForLevel(memberInfoResponse.memberLevel, isNonPLocked)
                val fineWaiverCount = memberInfoResponse.equity?.otherPark?.parkImpunity ?: 0
                
                    Log.d("MyMemberActivity", """
                        非P免罚数据详情:
                        - 免罚次数: $fineWaiverCount
                        - 是否锁定: $isNonPLocked
                        - 权益描述: $nonPRules
                        - 解锁状态资源ID: $unlockStatusResId
                        - 会员等级: ${memberInfoResponse.memberLevel}
                    """.trimIndent())
                
                if(::member2Frag.isInitialized) {
                    member2Frag.updateContent(nonPRules, fineWaiverCount, isNonPLocked)
                    member2Frag.updateNonParkingIcon(nonParkingIconResId)
                    member2Frag.updateUnlockStatusTitle(unlockStatusResId, true)
                        Log.d("MyMemberActivity", "已更新Member2Frag数据")
                    } else {
                        Log.w("MyMemberActivity", "Member2Frag未初始化")
                    }
                } else {
                    Log.w("MyMemberActivity", "非P免罚选项卡不可见")
                    if(::member2Frag.isInitialized) {
                        member2Frag.updateContent("", 0, true)
                        member2Frag.updateNonParkingIcon(0)
                        member2Frag.updateUnlockStatusTitle(0, false)
                    }
                }
            }
            2 -> { // 骑行卡延期
                Log.d("MyMemberActivity", "=== 处理骑行卡延时选项卡 ===")
                if (binding.lyMemberQxk.visibility == View.VISIBLE) {
                val rideCardRules = memberInfoResponse.equity?.rideCard?.desc ?: ""
                val isRideCardLocked = memberInfoResponse.equity?.rideCard?.isLock ?: true
                val unlockStatusResId = getUnlockStatusStringForLevel(memberInfoResponse.memberLevel, isRideCardLocked)
                val rideCardExtensionMinutes = memberInfoResponse.equity?.rideCard?.rideCardDelay ?: 0
                
                    Log.d("MyMemberActivity", """
                        骑行卡延时数据详情:
                        - 延时分钟数: $rideCardExtensionMinutes
                        - 是否锁定: $isRideCardLocked
                        - 权益描述: $rideCardRules
                        - 解锁状态资源ID: $unlockStatusResId
                        - 会员等级: ${memberInfoResponse.memberLevel}
                    """.trimIndent())
                
                if(::member3Frag.isInitialized) {
                    member3Frag.updateContent(rideCardRules, isRideCardLocked)
                    member3Frag.updateUnlockStatusTitle(unlockStatusResId, true)
                    member3Frag.updateRideCardTime(rideCardExtensionMinutes)
                        Log.d("MyMemberActivity", "已更新Member3Frag数据")
                    } else {
                        Log.w("MyMemberActivity", "Member3Frag未初始化")
                    }
                } else {
                    Log.w("MyMemberActivity", "骑行卡延时选项卡不可见")
                    if(::member3Frag.isInitialized) {
                        member3Frag.updateContent("", true)
                        member3Frag.updateUnlockStatusTitle(0, false)
                        member3Frag.updateRideCardTime(0)
                    }
                }
            }
        }
        
        // 更新底部ViewPager显示对应的Fragment
        binding.vp.setCurrentItem(index, false)
        
        // 更新选中指示箭头 - 只有可见的选项卡才显示箭头
        binding.ivArrowZsqb.visibility = if (index == 0 && binding.lyMemberZsqb.visibility == View.VISIBLE) View.VISIBLE else View.INVISIBLE
        binding.ivArrowFpmf.visibility = if (index == 1 && binding.lyMemberFpmf.visibility == View.VISIBLE) View.VISIBLE else View.INVISIBLE
        binding.ivArrowQxk.visibility = if (index == 2 && binding.lyMemberQxk.visibility == View.VISIBLE) View.VISIBLE else View.INVISIBLE
        
        Log.d("MyMemberActivity", "选项卡切换完成，当前选项卡: $index")
        
        // 每次切换后进行权益说明诊断
        debugEquityDescriptions()
    }

    /**
     * 拉取会员权益数据，组装卡片和权益内容
     */
    private fun loadMemberInfoData() {
        val disposable = UserRClient.client.getMemberInfo()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy(
                onNext = { memberInfoList ->
                    Log.d("MyMemberActivity", "MemberInfo List Response: ${memberInfoList}")
                    this.memberInfoListGlobal = memberInfoList

                    if (memberInfoList.isNotEmpty()) {
                        val newBanelData = ArrayList<CustomBean>()
                        for (memberInfo in memberInfoList) {
                            val customBean = CustomBean()
                            customBean.name = memberInfo.name ?: getString(R.string.app_name)
                            customBean.memberLevel = memberInfo.memberLevel
                            customBean.starTime = memberInfo.startTime ?: 0L
                            customBean.endTime = memberInfo.endTime ?: 0L
                            customBean.distance = memberInfo.userMileage ?: 0f
                            val isLocked = memberInfo.equity?.coupons?.isLock ?: false
                            customBean.nextLevel = if (isLocked) {
                                memberInfo.maxMileage ?: 0f
                            } else {
                                memberInfo.upgradeMileage ?: 0f
                            }
                            // 新增：赋值 min_mileage 字段
                            customBean.min_mileage = memberInfo.minMileage ?: 0f

                            val imagesRes = getMemberImageResources(memberInfo.memberLevel)
                            customBean.imageBg = imagesRes.mainImage
                            customBean.imageRes = imagesRes.hg
                            customBean.imageQues = imagesRes.ques
                            customBean.imageGo = imagesRes.go

                            // 新增：赋值isLocked字段，取主权益coupons的isLock
                            customBean.isLocked = isLocked

                            newBanelData.add(customBean)
                        }
                        this.banelData.clear()
                        this.banelData.addAll(newBanelData)
                        mViewPager.refreshData(this.banelData)
                        // BannerViewPager刷新后会自动回调onPageSelected，进而刷新底部权益内容
                        if (this.banelData.isNotEmpty()) {
                            selectTab(binding.vp.currentItem)
                        }
                        
                        // 调试：诊断青铜会员券包问题
                        debugBronzeMemberCoupons()
                        
                        // 调试：诊断权益说明更新问题
                        debugEquityDescriptions()

                    } else {
                        Log.d("MyMemberActivity", "MemberInfo List is empty - displaying empty/default state")
                        this.banelData.clear()
                        this.memberInfoListGlobal = emptyList()
                        mViewPager.refreshData(this.banelData)
                        // 没有数据时清空UI
                        binding.tvMemberQy.text = ""
                        binding.tvMemberQy.text = ""
                        if (::member1Frag.isInitialized) member1Frag.updateCoupons(emptyList())
                    }
                },
                onError = { error ->
                    Log.e("MyMemberActivity", "Error fetching member info", error)
                    Toast.makeText(this, "Failed to load member data: ${error.message}", Toast.LENGTH_LONG).show()
                    this.banelData.clear()
                    this.memberInfoListGlobal = emptyList()
                    mViewPager.refreshData(this.banelData) 
                }
            )
        compositeDisposable.add(disposable)
    }

    /**
     * 初始化顶部会员卡片轮播控件
     */
    private fun setupViewPager() {
        mViewPager = findViewById(R.id.banner_view)
        mViewPager.apply {
            adapter = BanelMemberAdapter() // 会员卡片适配器
            setLifecycleRegistry(lifecycle)
        }.setPageStyle(PageStyle.MULTI_PAGE)
            .setCanLoop(false)
            .setIndicatorVisibility(View.VISIBLE)
            .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
            .setIndicatorSlideMode(IndicatorSlideMode.COLOR)
            .setIndicatorSliderWidth(CommonUtils.dp2px(ContextUtil.getContext(), 8))
            .setIndicatorSliderGap(CommonUtils.dp2px(ContextUtil.getContext(), 1))
            .setIndicatorSliderColor(resources.getColor(R.color.white50),resources.getColor(R.color.white))
            .setRevealWidth(CommonUtils.dp2px(ContextUtil.getContext(), 12))
            .setPageMargin(CommonUtils.dp2px(ContextUtil.getContext(), 8))
            .setAutoPlay(false).create()
    }

    /**
     * 调试方法：诊断青铜会员券包显示问题
     * 可以在需要时调用此方法来获取详细的诊断信息
     */
    private fun debugBronzeMemberCoupons() {
        Log.d("MyMemberActivity", "=== 青铜会员券包诊断开始 ===")
        
        if (memberInfoListGlobal.isEmpty()) {
            Log.e("MyMemberActivity", "诊断失败: 会员数据列表为空")
            return
        }
        
        val bronzeMemberInfo = memberInfoListGlobal.find { it.memberLevel == 1 }
        if (bronzeMemberInfo == null) {
            Log.w("MyMemberActivity", "未找到青铜会员数据")
            return
        }
        
        Log.d("MyMemberActivity", """
            青铜会员基本信息:
            - 会员名称: ${bronzeMemberInfo.name}
            - 会员等级: ${bronzeMemberInfo.memberLevel}
            - 用户里程: ${bronzeMemberInfo.userMileage}
            - 最小里程要求: ${bronzeMemberInfo.minMileage}
            - 开始时间: ${bronzeMemberInfo.startTime}
            - 结束时间: ${bronzeMemberInfo.endTime}
            - 是否配置权益: ${bronzeMemberInfo.isConfigEquity}
        """.trimIndent())
        
        val equity = bronzeMemberInfo.equity
        if (equity == null) {
            Log.e("MyMemberActivity", "青铜会员没有权益数据")
            return
        }
        
        val coupons = equity.coupons
        if (coupons == null) {
            Log.e("MyMemberActivity", "青铜会员没有券包权益数据")
            return
        }
        
        Log.d("MyMemberActivity", """
            青铜会员券包权益:
            - 是否锁定: ${coupons.isLock}
            - 权益描述: ${coupons.desc}
            - 券包列表数量: ${coupons.list?.size ?: 0}
        """.trimIndent())
        
        coupons.list?.forEachIndexed { index, couponItem ->
            Log.d("MyMemberActivity", """
                券包 $index 详情:
                - ID: ${couponItem.userCouponId}
                - 名称: ${couponItem.couponInfo?.name}
                - 状态: ${couponItem.status}
                - 开始时间: ${couponItem.startTime}
                - 结束时间: ${couponItem.endTime}
                - 类型: ${couponItem.type}
                - 金额: ${couponItem.amount}
                - 业务类型: ${couponItem.businessType}
                - 券包信息: ${couponItem.couponInfo}
            """.trimIndent())
            
            // 模拟转换逻辑
            val endTimeMillis = (couponItem.endTime ?: 0L) * 1000L
            val currentTime = System.currentTimeMillis()
            val isExpired = endTimeMillis > 0 && endTimeMillis < currentTime
            
            Log.d("MyMemberActivity", """
                券包 $index 状态分析:
                - 原始endTime: ${couponItem.endTime}
                - 转换后endTime: $endTimeMillis
                - 当前时间: $currentTime
                - 是否过期(时间): $isExpired
                - 过期时间差: ${if (isExpired) currentTime - endTimeMillis else "未过期"}ms
            """.trimIndent())
        }
        
        Log.d("MyMemberActivity", "=== 青铜会员券包诊断结束 ===")
    }

    /**
     * 调试方法：诊断权益说明更新问题
     * 检查不同会员等级的权益说明是否正确更新
     */
    private fun debugEquityDescriptions() {
        Log.d("MyMemberActivity", "=== 权益说明诊断开始 ===")
        
        if (memberInfoListGlobal.isEmpty()) {
            Log.e("MyMemberActivity", "诊断失败: 会员数据列表为空")
            return
        }
        
        memberInfoListGlobal.forEachIndexed { index, memberInfo ->
            Log.d("MyMemberActivity", """
                会员等级 ${memberInfo.memberLevel} (索引$index) 权益说明:
                - 会员名称: ${memberInfo.name}
                - 专属券包描述: ${memberInfo.equity?.coupons?.desc ?: "无"}
                - 非P免罚描述: ${memberInfo.equity?.otherPark?.desc ?: "无"}
                - 骑行卡延时描述: ${memberInfo.equity?.rideCard?.desc ?: "无"}
                - 专属券包锁定: ${memberInfo.equity?.coupons?.isLock}
                - 非P免罚锁定: ${memberInfo.equity?.otherPark?.isLock}
                - 骑行卡延时锁定: ${memberInfo.equity?.rideCard?.isLock}
            """.trimIndent())
        }
        
        // 检查当前选中的会员卡片
        val currentPosition = mViewPager.currentItem
        if (currentPosition < memberInfoListGlobal.size) {
            val currentMember = memberInfoListGlobal[currentPosition]
            Log.d("MyMemberActivity", """
                当前选中会员等级: ${currentMember.memberLevel}
                当前ViewPager位置: $currentPosition
                当前权益选项卡: ${binding.vp.currentItem}
            """.trimIndent())
        }
        
        Log.d("MyMemberActivity", "=== 权益说明诊断结束 ===")
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable.dispose()
    }
}
// ===================== 会员中心页面 Activity END =====================