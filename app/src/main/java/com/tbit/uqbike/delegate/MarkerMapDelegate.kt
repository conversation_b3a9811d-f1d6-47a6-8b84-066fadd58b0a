package com.tbit.uqbike.delegate

import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.R
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IMapDelegate

class MarkerMapDelegate<T> : IMapDelegate {

    var updateMarkerListener = { _: MarkerWrapper, _: T -> }
    private var map: IBaseMap? = null
    internal val markers = mutableListOf<MarkerWrapper>()
    private val mapFactory get() = FlavorConfig.mapFactory
    private val defaultPoint = LatLng(0.0, 0.0)
    private val defaultRes by lazy { mapFactory.createResDescriptor(R.drawable.ic_normal_vehicle) }

    override fun setMap(map: IBaseMap?) {
        if (this.map == map) return
        cleanMarkers()
        this.map = map
    }

    fun setData(data: List<T>) {
        updateMarkers(data)
    }

    override fun fitMapView() {
        val map = map ?: return
        val points = markers.map { it.getPosition() }
        if (points.size >= 2) {
            map.fitMapView(points)
        } else if (points.size == 1) {
            map.moveToWithZoom(points.first(), 16f)
        }
    }

    private fun updateMarkers(data: List<T>) {
        val map = map ?: return
        if (markers.size > data.size) {
            val subList = markers.subList(data.size, markers.size)
            subList.forEach { it.remove() }
            subList.clear()
        }

        data.forEachIndexed { index, t ->
            val marker = markers.getOrNull(index)
                ?: (createMarker(map).apply { markers.add(this) })
            updateMarkerListener(marker, t)
        }
    }

    public fun cleanMarkers() {
        markers.forEach { it.remove() }
        markers.clear()
    }

    private fun createMarker(map: IBaseMap): MarkerWrapper {
        return map.createMarker(
            mapFactory.createMarkerOption()
                .position(defaultPoint)
                .icon(defaultRes)
        )
    }
}