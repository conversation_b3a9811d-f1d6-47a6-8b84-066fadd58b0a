package com.tbit.uqbike.delegate

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.tbit.maintanenceplus.utils.Selector
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.IBitmapDescriptor
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.tbituser.map.listener.OnMarkerClickListener
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.BikeState
import com.tbit.uqbike.bean.ParkPoint
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IParkPointMapDelegate
import com.tbit.uqbike.utils.MyLogUtil

class ParkPointMapDelegate: IParkPointMapDelegate, OnMarkerClickListener {

    var onMarkerClickListener:((<PERSON><PERSON><PERSON><PERSON><PERSON>, ParkPoint) -> Boolean)? = null
    private var map: IBaseMap? = null
    private val delegate = MarkerMapDelegate<ParkPoint>()
    private val mapFactory get() = FlavorConfig.mapFactory
    private val machineCount0 by lazy { createRes(R.drawable.ic_station_with0) }
    private val machineCount1 by lazy { createRes(R.drawable.ic_station_with0) }
    private val machineCount2 by lazy { createRes(R.drawable.ic_station_with0) }
    private val machineCount3 by lazy { createRes(R.drawable.ic_station_with0) }
    private val machineCount4 by lazy { createRes(R.drawable.ic_station_with0) }
    private val machineCount5 by lazy { createRes(R.drawable.ic_station_with0) }
    private val machineCount5More by lazy { createRes(R.drawable.ic_station_with0) }
    private val markerSelector = Selector<MarkerWrapper>(::onMarkerSelected, ::onMarkerUnselected)

    init {
        delegate.updateMarkerListener = ::updateMarker
    }

    private fun createRes(resId: Int): Pair<IBitmapDescriptor, Bitmap> {
        val bitmap = BitmapFactory.decodeResource(ContextUtil.getContext().resources, resId)
        return mapFactory.createBitmapDescriptor(bitmap) to bitmap
    }

    override fun setMap(map: IBaseMap?) {
        delegate.setMap(map)
        if (this.map == map) return
        this.map?.removeOnMarkerClickListener(this)
        map?.addOnMarkerClickListener(this)
        this.map = map
    }

    override fun fitMapView() {
        delegate.fitMapView()
    }

    override fun onMarkerClick(marker: MarkerWrapper): Boolean {
        val onMarkerClickListener = onMarkerClickListener ?: return false
        val parkPoint = (marker.getExtraInfo() as? ParkPoint) ?: return false
        return onMarkerClickListener(marker, parkPoint)
    }

    override fun select(marker: MarkerWrapper?) {
        markerSelector.setSelected(marker)
    }

    private fun onMarkerSelected(marker: MarkerWrapper?) {
        val state = (marker?.getExtraInfo() as? ParkPoint) ?: return
        marker.setIcon(getIconRes(state, 1.5f))
    }

    private fun onMarkerUnselected(marker: MarkerWrapper?) {
        val state = (marker?.getExtraInfo() as? ParkPoint) ?: return
        marker.setIcon(getIconRes(state).first)
    }

    override fun setParkPoints(parkPoints: List<ParkPoint>) {
//        MyLogUtil.Log("1111","==ParkPointMapDelegate  setParkPoints=="+parkPoints.size)
        delegate.setData(parkPoints)
    }

    private fun updateMarker(marker: MarkerWrapper, parkPoint: ParkPoint) {
        val latLng = LatLng(parkPoint.latC, parkPoint.lonC)
        marker.setPosition(latLng)
        marker.setIcon(getIconRes(parkPoint).first)
        marker.setExtraInfo(parkPoint)
    }

    private fun getIconRes(parkPoint: ParkPoint): Pair<IBitmapDescriptor, Bitmap> {
        return when (parkPoint.canBorrowNum) {
            0 -> machineCount0
            1 -> machineCount1
            2 -> machineCount2
            3 -> machineCount3
            4 -> machineCount4
            5 -> machineCount5
            else -> machineCount5More
        }
    }

    private fun getIconRes(parkPoint: ParkPoint, scale: Float): IBitmapDescriptor {
        val bitmap = getIconRes(parkPoint).second
        val scaleBitmap =
            Bitmap.createScaledBitmap(bitmap, (bitmap.width * scale).toInt(), (bitmap.height * scale).toInt(), false)
        return mapFactory.createBitmapDescriptor(scaleBitmap)
    }

    fun clear(){
        delegate.cleanMarkers()
    }
}