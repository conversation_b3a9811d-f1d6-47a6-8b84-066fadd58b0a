package com.tbit.uqbike.delegate

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.tbit.maintanenceplus.utils.Selector
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.IBitmapDescriptor
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.tbituser.map.listener.OnMarkerClickListener
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.ParkPoint
import com.tbit.uqbike.bean.ProhibitArea
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IProhibitAreaMapDelegate

class ProhibitAreaMapDelegate: IProhibitAreaMapDelegate, OnMarkerClickListener {

    var onMarkerClickListener:((<PERSON><PERSON><PERSON><PERSON><PERSON>, ProhibitArea) -> Boolean)? = null
    private var map: IBaseMap? = null
    private val delegate = MarkerMapDelegate<ProhibitArea>()
    private val mapFactory get() = FlavorConfig.mapFactory
    private val prohibitAreaRes by lazy { createRes(R.drawable.icon_prohibit_area) }
    private val markerSelector = Selector<MarkerWrapper>(::onMarkerSelected, ::onMarkerUnselected)

    init {
        delegate.updateMarkerListener = ::updateMarker
    }

    private fun createRes(resId: Int): Pair<IBitmapDescriptor, Bitmap> {
        val bitmap = BitmapFactory.decodeResource(ContextUtil.getContext().resources, resId)
        return mapFactory.createBitmapDescriptor(bitmap) to bitmap
    }

    override fun setMap(map: IBaseMap?) {
        delegate.setMap(map)
        if (this.map == map) return
        this.map?.removeOnMarkerClickListener(this)
        map?.addOnMarkerClickListener(this)
        this.map = map
    }

    override fun fitMapView() {
        delegate.fitMapView()
    }

    override fun onMarkerClick(marker: MarkerWrapper): Boolean {
        val onMarkerClickListener = onMarkerClickListener ?: return false
        val parkPoint = (marker.getExtraInfo() as? ProhibitArea) ?: return false
        return onMarkerClickListener(marker, parkPoint)
    }

    override fun select(marker: MarkerWrapper?) {
        markerSelector.setSelected(marker)
    }

    private fun onMarkerSelected(marker: MarkerWrapper?) {
        val state = (marker?.getExtraInfo() as? ProhibitArea) ?: return
        marker.setIcon(getIconRes(state, 1.5f))
    }

    private fun onMarkerUnselected(marker: MarkerWrapper?) {
        val state = (marker?.getExtraInfo() as? ProhibitArea) ?: return
        marker.setIcon(getIconRes(state).first)
    }

    override fun setProhibitAreas(prohibitAreas: List<ProhibitArea>) {
        delegate.setData(prohibitAreas)
    }

    private fun updateMarker(marker: MarkerWrapper, prohibitArea: ProhibitArea) {
        val latLng = LatLng(prohibitArea.latC, prohibitArea.lonC)
        marker.setPosition(latLng)
        marker.setIcon(getIconRes(prohibitArea).first)
        marker.setExtraInfo(prohibitArea)
    }

    private fun getIconRes(prohibitArea: ProhibitArea): Pair<IBitmapDescriptor, Bitmap> {
        return prohibitAreaRes
    }

    private fun getIconRes(prohibitArea: ProhibitArea, scale: Float): IBitmapDescriptor {
        val bitmap = getIconRes(prohibitArea).second
        val scaleBitmap =
            Bitmap.createScaledBitmap(bitmap, (bitmap.width * scale).toInt(), (bitmap.height * scale).toInt(), false)
        return mapFactory.createBitmapDescriptor(scaleBitmap)
    }
    fun clear(){
        delegate.cleanMarkers()
    }
}