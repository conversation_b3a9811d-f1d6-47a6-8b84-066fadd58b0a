package com.tbit.uqbike.delegate

import com.tbit.tbituser.map.base.CircleWrapper
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.tbituser.map.utils.SphericalUtil
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IMapDelegate

class CircleMapDelegate<T> : IMapDelegate {

    var updateCircleListener = { _: CircleWrapper, _: T -> }
    private var map: IBaseMap? = null
    internal val circles = mutableListOf<CircleWrapper>()
    private val mapFactory get() = FlavorConfig.mapFactory
    private val defaultCenter = LatLng(0.0, 0.0)

    override fun setMap(map: IBaseMap?) {
        if (this.map == map) return
        cleanCircles()
        this.map = map
    }

    override fun fitMapView() {
        val map = map ?: return
        val points = getFitMapViewPoints()
        map.fitMapView(points)
    }

    fun getFitMapViewPoints(): List<LatLng> {
        return circles.map { getCirclePoints(it.getCenter(), it.getRadius()) }.flatMap{ it }
    }

    private fun getCirclePoints(centre: LatLng, radius: Double): ArrayList<LatLng> {
        val points = ArrayList<LatLng>()
        points.add(SphericalUtil.computeOffset(centre, radius, 0.0))
        points.add(SphericalUtil.computeOffset(centre, radius, 90.0))
        points.add(SphericalUtil.computeOffset(centre, radius, -90.0))
        points.add(SphericalUtil.computeOffset(centre, radius, -180.0))
        return points
    }

    fun setData(data: List<T>) {
        updateCircles(data)
    }

    private fun updateCircles(data: List<T>) {
        val map = map ?: return
        if (circles.size > data.size) {
            val subList = circles.subList(data.size, circles.size)
            subList.forEach { it.remove() }
            subList.clear()
        }

        data.forEachIndexed { index, t ->
            val marker = circles.getOrNull(index)
                ?: (createCircle(map).apply { circles.add(this) })
            updateCircleListener(marker, t)
        }
    }

    private fun cleanCircles() {
        circles.forEach { it.remove() }
        circles.clear()
    }

    private fun createCircle(map: IBaseMap): CircleWrapper {
        return map.createCircle(
            mapFactory.createCircleOption()
                .setCenter(defaultCenter))
    }
}