package com.tbit.uqbike.delegate

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.tbit.maintanenceplus.utils.Selector
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.IBitmapDescriptor
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.tbituser.map.listener.OnMarkerClickListener
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.BikeState
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IMachineMapDelegate
import com.tbit.uqbike.entity.VehicleData
import com.tbit.uqbike.utils.MyLogUtil

class BikeMapDelegate : IMachineMapDelegate, OnMarkerClickListener {

    var onMarkerClickListener = { _: MarkerWrapper, _: BikeState -> false }
    private var map: IBaseMap? = null
    private val delegate = MarkerMapDelegate<BikeState>()
    private val mapFactory get() = FlavorConfig.mapFactory
    private val bikeRes by lazy { createRes(R.drawable.ic_normal_vehicle) }
    private val markerSelector = Selector<MarkerWrapper>(::onMarkerSelected, ::onMarkerUnselected)

    init {
        delegate.updateMarkerListener = ::updateMarker
    }

    private fun createRes(resId: Int): Pair<IBitmapDescriptor, Bitmap> {
        val bitmap = BitmapFactory.decodeResource(ContextUtil.getContext().resources, resId)
        return mapFactory.createBitmapDescriptor(bitmap) to bitmap
    }

    override fun setMap(map: IBaseMap?) {
        delegate.setMap(map)
        if (this.map == map) return
        this.map?.removeOnMarkerClickListener(this)
        map?.addOnMarkerClickListener(this)
        this.map = map
    }

    override fun fitMapView() {
        delegate.fitMapView()
    }

    override fun onMarkerClick(marker: MarkerWrapper): Boolean {
        MyLogUtil.Log("1111","========onMarkerClick=======")
//        val onMarkerClickListener = onMarkerClickListener ?: return false
        val state = (marker.getExtraInfo() as? BikeState) ?: return false
        return onMarkerClickListener(marker, state)
    }

//    override fun select(status: BikeState) {
//        MyLogUtil.Log("1111","======111111111111111111110000000000000123")
//        val marker = delegate.markers.firstOrNull { it.getExtraInfo() == status } ?: return
//        select(marker)
//    }

    override fun select(marker: MarkerWrapper?) {
        markerSelector.setSelected(marker)
    }

    private fun onMarkerSelected(marker: MarkerWrapper?) {
        val state = (marker?.getExtraInfo() as? BikeState) ?: return
        marker!!.setIcon(getIconRes(BikeState(), 1.5f))
    }

    private fun onMarkerUnselected(marker: MarkerWrapper?) {
        val state = (marker?.getExtraInfo() as? BikeState) ?: return
        marker!!.setIcon(getIconRes(BikeState()).first)
    }

    fun center(status: BikeState) {
        val marker = delegate.markers.firstOrNull { it.getExtraInfo() == status } ?: return
        delegate.markers.forEach {
            it.setVisible(it == marker)
        }
        map?.moveToWithZoom(marker.getPosition(), 16f)
    }

    override fun setBikeStates(bikeStates: List<BikeState>) {
        delegate.setData(bikeStates)
        select(null)
    }

    private fun updateMarker(marker: MarkerWrapper, state: BikeState) {
        val latLng = LatLng(state.latC, state.lonC)
        marker.setPosition(latLng)
        marker.setExtraInfo(state)
        marker.setIcon(getIconRes(state).first)
        marker.setVisible(true)
    }

    private inline fun getIconRes(state: BikeState): Pair<IBitmapDescriptor, Bitmap> {
        return bikeRes
    }

    private fun getIconRes(status: BikeState, scale: Float): IBitmapDescriptor {
        val bitmap = getIconRes(status).second
        val scaleBitmap =
            Bitmap.createScaledBitmap(bitmap, (bitmap.width * scale).toInt(), (bitmap.height * scale).toInt(), false)
        return mapFactory.createBitmapDescriptor(scaleBitmap)
    }

    fun setBickData(resultData: VehicleData) {
        val BikeStateList = mutableListOf<BikeState>()
        if(resultData.size <= 0) return
        resultData.forEach{
            var pointData = BikeState()
            pointData.mileageRemain = it.vehicle_no
            pointData.latC = it.lat
            pointData.lonC = it.lng
            pointData.powerRemain = it.battery_energy
            BikeStateList.add(pointData)
        }
        delegate.setData(BikeStateList)
        select(null)
    }
    fun clear(){
        delegate.cleanMarkers()
    }
}