package com.tbit.uqbike.delegate

import com.google.android.gms.maps.model.Dash
import com.google.android.gms.maps.model.Dot
import com.google.android.gms.maps.model.Gap
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.PolygonWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IMapDelegate
import com.tbit.uqbike.utils.MyLogUtil
import java.util.Arrays

class PolygonMapDelegate<T> : IMapDelegate {

    var updatePolygonListener = { _: PolygonWrapper, _: T -> }
    private var map: IBaseMap? = null
    internal val polygons = mutableListOf<PolygonWrapper>()
    private val mapFactory get() = FlavorConfig.mapFactory
    private val defaultPoint = LatLng(0.0, 0.0)
    private val defaultPoints = listOf(defaultPoint, defaultPoint, defaultPoint)
    var isDdtted = false;//是否虚线

    override fun setMap(map: IBaseMap?) {
        if (this.map == map) return
        cleanPolygons()
        this.map = map
    }

    override fun fitMapView() {
        val map = map ?: return
        val points = getFitMapViewPoints()
        if (points.size >= 2) {
            map.fitMapView(points)
        } else if (points.size == 1) {
            map.moveToWithZoom(points.first(), 16f)
        }
    }

    fun getFitMapViewPoints(): List<LatLng> {
        return polygons.map { it.getPoints() }.flatMap { it }.filter { it != defaultPoint }
    }

    fun setData(data: List<T>) {
//        MyLogUtil.Log("1111","==PolygonMapDelegate setData=="+data.size)
        updatePolygons(data)
    }

    private fun updatePolygons(data: List<T>) {
        val map = map ?: return
        cleanPolygons()
        if (polygons.size > data.size) {
            val subList = polygons.subList(data.size, polygons.size)
            subList.forEach { it.remove() }
            subList.clear()
        }

        data.forEachIndexed { index, t ->
            val marker = polygons.getOrNull(index)
                ?: (createPolygon(map).apply {polygons.add(this)
                })
            updatePolygonListener(marker, t)
        }
    }

    private fun cleanPolygons() {
        polygons.forEach { it.remove() }
        polygons.clear()
    }

    private fun createPolygon(map: IBaseMap): PolygonWrapper {
        if(isDdtted){
            return map.createPolygon(mapFactory.createPolygonOptionByDotted().setPoints(defaultPoints))
        }else{
            return map.createPolygon(mapFactory.createPolygonOption().setPoints(defaultPoints))
        }
    }
}