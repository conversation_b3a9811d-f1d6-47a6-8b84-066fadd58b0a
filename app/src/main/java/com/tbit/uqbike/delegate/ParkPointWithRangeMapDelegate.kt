package com.tbit.uqbike.delegate

import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.ParkPoint
import com.tbit.uqbike.delegate.interfaces.IParkPointWithRangeMapDelegate
import com.tbit.uqbike.entity.getAreaDataItem
import com.tbit.uqbike.utils.MyLogUtil

class ParkPointWithRangeMapDelegate: IParkPointWithRangeMapDelegate {

    private var map: IBaseMap? = null
    var onMarkerClickListener:((MarkerWrapper, ParkPoint) -> Boolean)? = null
    override var drawRangeFilter = { parkPoints :List<ParkPoint> -> parkPoints }

    private val strokeWidth = ContextUtil.getContext().resources.getDimension(R.dimen.stroke_width)
    private val strokeColor = ContextUtil.getContext().resources.getColor(R.color.parkPointColorStroke)
    private val fillColor = ContextUtil.getContext().resources.getColor(R.color.parkPointColorFill)

    private val parkPointMapDelegate = ParkPointMapDelegate()  //停车点
//    private val circleMapDelegate = CircleMapDelegate<Pair<LatLng, Double>>()
//    private val polygonMapDelegate = PolygonMapDelegate<List<LatLng>>()
    private val polygonMapDelegate = PolygonMapDelegate<getAreaDataItem>()

    init {
//        circleMapDelegate.updateCircleListener = { circle, pair ->
//            circle.setCenter(pair.first)
//            circle.setRadius(pair.second)
//            circle.setFillColor(fillColor)
//            circle.setStrokeColor(strokeColor)
//            circle.setStrokeWidth(strokeWidth)
//        }

        polygonMapDelegate.updatePolygonListener = { polygon, points ->
            polygon.setPoints(points.coordinates)
            polygon.setFillColor(fillColor)
            polygon.setStrokeColor(strokeColor)
            polygon.setStokeWidth(strokeWidth)
        }

        parkPointMapDelegate.onMarkerClickListener = {marker, prakPoint ->
            onMarkerClickListener?.invoke(marker, prakPoint) ?: false
        }
    }

    override fun setMap(map: IBaseMap?) {
        this.map = map
        parkPointMapDelegate.setMap(map)
//        circleMapDelegate.setMap(map)
        polygonMapDelegate.setMap(map)
    }

    override fun fitMapView() {
        val map = map ?: return
//        val points = circleMapDelegate.getFitMapViewPoints() + polygonMapDelegate.getFitMapViewPoints()
        val points = polygonMapDelegate.getFitMapViewPoints()
        map.fitMapView(points)
    }

    override fun setParkPoints(parkPoints: List<ParkPoint>) {
//        parkPointMapDelegate.setParkPoints(parkPoints)

        val filterParkPoints = drawRangeFilter(parkPoints)

//        val circleParkPoints = filterParkPoints.filter { it.parkPointType == Constant.PointType.CIRCLE }
//        circleMapDelegate.setData(circleParkPoints.map { it.let { LatLng(it.latC, it.lonC) } to it.range })

//        val polygonParkPoints = filterParkPoints.filter { it.parkPointType == Constant.PointType.POLYGON }
//        polygonMapDelegate.setData(polygonParkPoints.map { it.pointsC })
    }

    override fun select(marker: MarkerWrapper?) {
        parkPointMapDelegate.select(marker)
    }
    fun setPartAreaData(geoList: List<getAreaDataItem>) {
        polygonMapDelegate.setData(geoList)
        val ParkPointList = mutableListOf<ParkPoint>()
        if(geoList.size <= 0) return
        geoList.forEach{
            var pointData = ParkPoint()
            pointData.parkPointId = it.id
            pointData.name = it.name
            pointData.region_code = it.region_code
            pointData.latC = it.flag.lat
            pointData.lonC = it.flag.lng
            pointData.pointsC = it.coordinates
            ParkPointList.add(pointData)
        }
        parkPointMapDelegate.setParkPoints(ParkPointList)
    }
    fun clear(){
        parkPointMapDelegate.clear()
        setPartAreaData(emptyList())
    }
}