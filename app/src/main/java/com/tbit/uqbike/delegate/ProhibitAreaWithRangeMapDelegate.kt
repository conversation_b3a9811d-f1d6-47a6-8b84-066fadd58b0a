package com.tbit.uqbike.delegate

import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.ParkPoint
import com.tbit.uqbike.bean.ProhibitArea
import com.tbit.uqbike.delegate.interfaces.IProhibitAreaWithRangeMapDelegate
import com.tbit.uqbike.entity.getAreaDataItem

class ProhibitAreaWithRangeMapDelegate: IProhibitAreaWithRangeMapDelegate {

    private var map: IBaseMap? = null
    var onMarkerClickListener:((MarkerWrapper, ProhibitArea) -> Boolean)? = null
    override var drawRangeFilter = { prohibitAreas :List<ProhibitArea> -> prohibitAreas }

    private val strokeWidth = ContextUtil.getContext().resources.getDimension(R.dimen.stroke_width)
    private val strokeColor = ContextUtil.getContext().resources.getColor(R.color.prohibitAreaColorStroke)
    private val fillColor = ContextUtil.getContext().resources.getColor(R.color.prohibitAreaColorFill)

    private val prohibitAreaMapDelegate = ProhibitAreaMapDelegate()
//    private val circleMapDelegate = CircleMapDelegate<Pair<LatLng, Double>>()
//    private val polygonMapDelegate = PolygonMapDelegate<List<LatLng>>()
    private val polygonMapDelegate = PolygonMapDelegate<getAreaDataItem>()

    init {
//        circleMapDelegate.updateCircleListener = { circle, pair ->
//            circle.setCenter(pair.first)
//            circle.setRadius(pair.second)
//            circle.setFillColor(fillColor)
//            circle.setStrokeColor(strokeColor)
//            circle.setStrokeWidth(strokeWidth)
//        }

//        polygonMapDelegate.updatePolygonListener = { polygon, points ->
//            polygon.setPoints(points)
//            polygon.setFillColor(fillColor)
//            polygon.setStrokeColor(strokeColor)
//            polygon.setStokeWidth(strokeWidth)
//        }

        prohibitAreaMapDelegate.onMarkerClickListener = { marker, prohibitArea ->
            onMarkerClickListener?.invoke(marker, prohibitArea) ?: false
        }

        polygonMapDelegate.updatePolygonListener = { polygon, points ->
            polygon.setPoints(points.coordinates)
            polygon.setFillColor(fillColor)
            polygon.setStrokeColor(strokeColor)
            polygon.setStokeWidth(strokeWidth)
        }
    }

    override fun setMap(map: IBaseMap?) {
        this.map = map
        prohibitAreaMapDelegate.setMap(map)
//        circleMapDelegate.setMap(map)
        polygonMapDelegate.setMap(map)
    }

    override fun fitMapView() {
        val map = map ?: return
//        val points = circleMapDelegate.getFitMapViewPoints() + polygonMapDelegate.getFitMapViewPoints()
        val points = polygonMapDelegate.getFitMapViewPoints()
        map.fitMapView(points)
    }

    override fun setProhibitAreas(prohibitAreas: List<ProhibitArea>) {
//        prohibitAreaMapDelegate.setProhibitAreas(prohibitAreas)
//
//        val filterProhibitAreas = drawRangeFilter(prohibitAreas)
//
//        val circleProhibitAreas = filterProhibitAreas.filter { it.prohibitAreaType == Constant.PointType.CIRCLE }
//        circleMapDelegate.setData(circleProhibitAreas.map { it.let { LatLng(it.latC, it.lonC) } to it.range })
//
//        val polygonProhibitAreas = filterProhibitAreas.filter { it.prohibitAreaType == Constant.PointType.POLYGON }
//        polygonMapDelegate.setData(polygonProhibitAreas.map { it.pointsC })
    }

    override fun select(marker: MarkerWrapper?) {
//        prohibitAreaMapDelegate.select(marker)
    }

    fun setProhibitAreaData(geoList: List<getAreaDataItem>) {
        polygonMapDelegate.setData(geoList)
        val ProhibitAreaList = mutableListOf<ProhibitArea>()
        if(geoList.size <= 0) return
        geoList.forEach{
            var pointData = ProhibitArea()
            pointData.prohibitAreaId = it.id
            pointData.name = it.name
            pointData.region_code = it.region_code
            pointData.latC = it.flag.lat
            pointData.lonC = it.flag.lng
            pointData.pointsC = it.coordinates
            ProhibitAreaList.add(pointData)
        }
        prohibitAreaMapDelegate.setProhibitAreas(ProhibitAreaList)
    }
    fun clear(){
        prohibitAreaMapDelegate.clear()
        setProhibitAreaData(emptyList())
    }
}