package com.tbit.uqbike.delegate

import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.delegate.interfaces.IMapDelegate

class SimplePolygonMapDelegate(
    private val fillColor: Int,
    private val strokeColor: Int,
    private val strokeWidth: Float) : IMapDelegate {

    private val delegate = PolygonMapDelegate<List<LatLng>>()

    init {
        delegate.updatePolygonListener = { polygon, points ->
            polygon.setPoints(points)
            polygon.setFillColor(fillColor)
            polygon.setStrokeColor(strokeColor)
            polygon.setStokeWidth(strokeWidth)
        }
    }

    override fun setMap(map: IBaseMap?) {
        delegate.setMap(map)
    }

    override fun fitMapView() {
        delegate.fitMapView()
    }

    fun setPoints(points: List<LatLng>) {
        if (points.size >= 2) {
            delegate.setData(listOf(points))
        } else {
            delegate.setData(emptyList())
        }
    }
}