package com.tbit.uqbike.delegate

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.base.IBitmapDescriptor
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.base.PolylineWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.App
import com.tbit.uqbike.R
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.interfaces.IMapDelegate


class PolylineMapDelegate: IMapDelegate {

    var polylineColor:Int? = App.context.resources.getColor(R.color.blue_namal)
    private var map: IBaseMap? = null
    private var startMarker: MarkerWrapper? = null
    private var endMarker: MarkerWrapper? = null
    private var polyline: <PERSON>ylineWrapper? = null
    private var isRental = false
    private val mapFactory get() = FlavorConfig.mapFactory
    private val startRes by lazy { mapFactory.createResDescriptor(R.drawable.ic_start_point) }
    private val endRes by lazy { mapFactory.createResDescriptor(R.drawable.ic_end_point) }

    override fun setMap(map: IBaseMap?) {
        if(this.map == map) return
        cleanMarkers()
        cleanPolyline()
        this.map = map
    }

    override fun fitMapView() {
        val map = map ?: return
        if (isRental){
            var myPos = ArrayList<LatLng>()
            if (endMarker!=null) {
                myPos.add(endMarker!!.getPosition())
            }else{
                if (startMarker!=null) myPos.add(startMarker!!.getPosition())
            }
            map.fitMapView(myPos)
        }else{
            val points = polyline?.getPoints() ?: return
            if (points.size >= 2) {
                map.fitMapView(points)
            }
        }
    }

    fun setPoints(points: List<LatLng>,isRental : Boolean) {
        this.isRental = isRental
        try {
            if (points.size >= 1) {
                updateMarkers(points)
                if (!isRental){
                    updatePolyline(points)
                }
            } else {
                cleanMarkers()
                cleanPolyline()
            }
        }catch (e : Exception){}

    }

    private fun updateMarkers(points: List<LatLng>) {
        val map = map ?: return

//        var view: View? = LayoutInflater.from(App.context).inflate(R.layout.item_message, null, false)
//        var bitmap = Bitmap.createBitmap(view!!.getWidth(), view!!.getHeight(), Bitmap.Config.ARGB_8888);
//        var canvas = Canvas(bitmap);
//        canvas.drawColor(Color.WHITE);
//        view.draw(canvas);
//        var startRes = mapFactory.createBitmapDescriptor(bitmap)

        try {
            val startPoint = points.first()
            val startMarker = startMarker ?: createMarker(map, startPoint)
            startMarker.setPosition(startPoint)
            startMarker.setIcon(startRes!!)
            this.startMarker = startMarker

            val endPoint = points.last()
            val endMarker = endMarker ?: createMarker(map, endPoint)
            endMarker.setPosition(endPoint)
            endMarker.setIcon(endRes)
            this.endMarker = endMarker
        }catch ( e : Exception){}

    }

    private fun cleanMarkers() {
        startMarker?.remove()
        startMarker = null
        endMarker?.remove()
        endMarker = null
    }

    private fun createMarker(map: IBaseMap, latLng: LatLng): MarkerWrapper {
        return map.createMarker(
            mapFactory.createMarkerOption()
                .position(latLng)
                .draggable(true)
                .icon(startRes!!))
    }

    private fun updatePolyline(points: List<LatLng>) {
        val map = map ?: return
        val polyline = polyline ?: createPolyline(map, points)
        polyline.setPoints(points)
        polylineColor?.let { polyline.setColor(it) }
        this.polyline = polyline
    }

    private fun cleanPolyline() {
        polyline?.remove()
        polyline = null
    }

    private fun createPolyline(map: IBaseMap, latLngs: List<LatLng>): PolylineWrapper {
        return map.createPolyline(
            mapFactory.createPolylineOption()
                .points(latLngs))
    }
}