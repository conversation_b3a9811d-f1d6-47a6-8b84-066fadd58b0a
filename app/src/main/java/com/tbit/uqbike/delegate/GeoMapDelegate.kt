package com.tbit.uqbike.delegate

import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.Geo
import com.tbit.uqbike.delegate.interfaces.IMapDelegate
import com.tbit.uqbike.entity.getAreaDataItem
import com.tbit.uqbike.utils.MyLogUtil

/**
 * 电子围栏类型0：圆形1：多边形
 */
class GeoMapDelegate : IMapDelegate {

    private val strokeWidth = ContextUtil.getContext().resources.getDimension(R.dimen.stroke_width_2)
    private val strokeColor = ContextUtil.getContext().resources.getColor(R.color.geoColorStroke)
    private val fillColor = ContextUtil.getContext().resources.getColor(R.color.geoColorFill)
    private var map: IBaseMap? = null
//    private val circleMapDelegate = CircleMapDelegate<Geo>()
//    private val polygonMapDelegate = PolygonMapDelegate<Geo>()

//    private val circleMapDelegate = CircleMapDelegate<Geo>()
    private val polygonMapDelegate = PolygonMapDelegate<getAreaDataItem>()

    init {
//        circleMapDelegate.updateCircleListener = { circle, geo ->
//            val center = geo.pointsC.firstOrNull() ?: LatLng(0.0, 0.0)
//            circle.setCenter(center)
//            circle.setRadius(geo.radius.toDouble())
//            circle.setFillColor(fillColor)
//            circle.setStrokeColor(strokeColor)
//            circle.setStrokeWidth(strokeWidth)
//        }
//
//        polygonMapDelegate.updatePolygonListener = { polygon, geo ->
//            polygon.setPoints(geo.pointsC)
//            polygon.setFillColor(fillColor)
//            polygon.setStrokeColor(strokeColor)
//            polygon.setStokeWidth(strokeWidth)
//        }
        polygonMapDelegate.isDdtted = true
        polygonMapDelegate.updatePolygonListener = { polygon, geo ->
            polygon.setPoints(geo.coordinates)
            polygon.setFillColor(fillColor)
            polygon.setStrokeColor(strokeColor)
            polygon.setStokeWidth(strokeWidth)
        }
    }

    override fun setMap(map: IBaseMap?) {
        this.map = map
//        circleMapDelegate.setMap(map)
        polygonMapDelegate.setMap(map)
    }

    override fun fitMapView() {
        val map = map ?: return
//        val points = circleMapDelegate.getFitMapViewPoints() + polygonMapDelegate.getFitMapViewPoints()
        val points = polygonMapDelegate.getFitMapViewPoints()
        map.fitMapView(points)
    }

    fun setGeo(geoList: List<Geo>) {
//        val circleGeo = geoList.filter { it.geoType == Constant.GeoType.CIRCLE }
//        val polygonGeo = geoList.filter { it.geoType == Constant.GeoType.POLYGON }
//        circleMapDelegate.setData(circleGeo)
//        polygonMapDelegate.setData(polygonGeo)
    }

    fun setGeoNew(geoList: List<getAreaDataItem>) {
        polygonMapDelegate.setData(geoList)
    }
}