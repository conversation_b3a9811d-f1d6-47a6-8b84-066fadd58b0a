package com.tbit.uqbike.delegate

import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.delegate.interfaces.IMapDelegate

class SimpleCircleMapDelegate(
    private val fillColor: Int,
    private val strokeColor: Int,
    private val strokeWidth: Float
) : IMapDelegate {

    private val delegate = CircleMapDelegate<Pair<LatLng, Double>>()

    init {
        delegate.updateCircleListener = { circle, pair ->
            circle.setCenter(pair.first)
            circle.setRadius(pair.second)
            circle.setFillColor(fillColor)
            circle.setStrokeColor(strokeColor)
            circle.setStrokeWidth(strokeWidth)
        }
    }

    override fun setMap(map: IBaseMap?) {
        delegate.setMap(map)
    }

    override fun fitMapView() {
        delegate.fitMapView()
    }

    fun setCircle(center: LatLng?, radius: Double) {
        if(center == null) {
            delegate.setData(emptyList())
        } else {
            delegate.setData(listOf(center to radius))
        }
    }
}