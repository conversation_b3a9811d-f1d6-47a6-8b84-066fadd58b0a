package com.tbit.uqbike.delegate

import com.tbit.maintenance.map.base.IRouteLine
import com.tbit.maintenance.map.base.RouteOverlayWrapper
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.uqbike.delegate.interfaces.IMapDelegate

class RouteLineMapDelegate: IMapDelegate {

    private var map: IBaseMap? = null
    private var routeOverlay: RouteOverlayWrapper? = null

    override fun setMap(map: IBaseMap?) {
        if(this.map == map) return
        clean()
        this.map = map
    }

    override fun fitMapView() {
        routeOverlay?.zoomToSpan()
    }

    fun setRouteLine(routeLine: IRouteLine) {
        clean()
        routeOverlay = map?.createRouteOverlay(routeLine)?.apply { addToMap() }
    }

    fun clean() {
        routeOverlay?.removeFromMap()
        routeOverlay = null
    }
}