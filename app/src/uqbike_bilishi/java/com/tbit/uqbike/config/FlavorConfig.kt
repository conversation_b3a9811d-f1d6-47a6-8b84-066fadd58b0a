package com.tbit.uqbike.config

import com.tbit.maintenance.factory.AAppMapFactory
import com.tbit.uqbike.factory.GAppMapFactory
import com.tbit.uqbike.route.ChinaAppRoute
import com.tbit.uqbike.route.OverseasAppRoute
import java.util.*

object FlavorConfig {

    object NET {

        //平台Id
        const val PLATFORM_ID = 2

        //服务器地址
        const val BASE_URL = "http://blstestclient.uqbike.cn/"

        //升级文件路径
        const val UPDATE_URL = "version/android/version_yqcx.xml"

        //升级描述文件路径
        const val UPGRADE_DESC_URL = "version/android/Explain_yqcx.xml"

        //goshop业务H5域名
        var GOSHOP_WEB_HOST = "https://www.gogo-shop.com"
    }

    object Local {
        val timeZone get() = TimeZone.getDefault()
        val language get() = Locale.getDefault().language ?: ""
        val country get() = Locale.getDefault().country ?: ""
        val displayCountry get() = Locale.getDefault().displayCountry ?: ""
        val displayCountryEn get() = Locale.getDefault().getDisplayCountry(Locale.US) ?: ""
        val isO3Country get() = Locale.getDefault().isO3Country ?: ""

        val serviceLanguage get() = if(language.equals("zh", true)) country else language

        val headerLang = serviceLanguage
        val urlLang = if ("zh".equals(language, true)) "cn" else "en"
    }

    val mapFactory get() = if(Local.language.equals("zh", true)) AAppMapFactory else GAppMapFactory

    val appRoute get() = if(Local.language.equals("zh", true)) ChinaAppRoute else OverseasAppRoute
}