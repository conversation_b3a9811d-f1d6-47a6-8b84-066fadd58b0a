package com.tbit.uqbike.config

import com.tbit.maintenance.factory.AAppMapFactory
import java.util.*

object FlavorConfig {

    object NET {

        //平台Id
        const val PLATFORM_ID = 78

        //服务器地址
        const val BASE_URL = "http://client.dahachuxing.com/"

        //升级文件路径
        const val UPDATE_URL = "version/android/version_dhcx.xml"

        //升级描述文件路径
        const val UPGRADE_DESC_URL = "version/android/Explain_dhcx.xml"

        //goshop业务H5域名
        var GOSHOP_WEB_HOST = "https://www.gogo-shop.com"
    }

    object Local {
        val timeZone get() = TimeZone.getDefault()
        val language get() = Locale.getDefault().language ?: ""
        val country get() = Locale.getDefault().country ?: ""

        //网络请求头部lang参数值
        val serviceLanguage get() = if(language.equals("zh", true)) country else language
    }

    //使用高德地图
    val mapFactory = AAppMapFactory
}